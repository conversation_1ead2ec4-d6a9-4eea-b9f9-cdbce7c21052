---
import Layout from '../layouts/Layout.astro';
import '@styles/animations.css';
---

<Layout title="About Us - 3Pay Global">
	<div class="relative w-full overflow-hidden">
		<!-- Hero Section -->
		<section class="relative min-h-screen flex items-center justify-center py-20 bg-[#003820]/5">
			<!-- Premium background effects -->
			<div class="absolute inset-0 overflow-hidden pointer-events-none">
				<div class="absolute top-0 left-1/4 w-[45rem] h-[45rem] bg-gradient-to-br from-[#003820]/10 to-[#003820]/5 rounded-full blur-[160px] mix-blend-soft-light animate-float-slow"></div>
				<div class="absolute bottom-0 right-1/4 w-[35rem] h-[35rem] bg-gradient-to-tr from-[#003820]/15 to-[#003820]/5 rounded-full blur-[128px] mix-blend-soft-light animate-float-reverse"></div>
				<div class="absolute inset-0 bg-grid opacity-[0.015] mix-blend-overlay"></div>
				<div class="absolute inset-0 pointer-events-none overflow-hidden">
					<div class="absolute top-1/4 left-1/4 w-64 h-64 bg-[#003820]/10 rounded-full filter blur-[100px] animate-pulse-slow"></div>
					<div class="absolute bottom-1/4 right-1/4 w-96 h-96 bg-[#003820]/5 rounded-full filter blur-[120px] animate-pulse-slow delay-1000"></div>
					<div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] bg-[#003820]/5 rounded-full filter blur-[150px] animate-pulse-slow delay-2000"></div>
				</div>
			</div>

			<div class="container mx-auto px-4 relative z-10">
				<div class="max-w-4xl mx-auto text-center">
					<div class="mb-6 animate-fade-in">
						<span class="inline-block px-4 py-2 bg-[#003820]/10 text-[#003820] rounded-full text-sm font-semibold mb-4">
							About 3Pay Global
						</span>
					</div>
					<h1 class="text-5xl md:text-7xl font-bold mb-6 animate-gradient bg-[length:200%_auto] bg-clip-text text-transparent bg-gradient-to-r from-[#003820] via-[#005732] to-[#003820]">
						About 3Pay Global
					</h1>
					<p class="text-xl md:text-2xl text-[#3C4145]/80 max-w-2xl mx-auto animate-fade-in delay-100">
						Strategic litigation funding for strong legal cases with limited financial resources
					</p>
				</div>
			</div>
			<div class="absolute bottom-0 left-0 w-full h-32 bg-gradient-to-t from-[#003820]/20 to-transparent pointer-events-none"></div>
		</section>

		<!-- Who We Are Section -->
		<section class="py-24 bg-white relative overflow-hidden">
			<div class="absolute inset-0 bg-gradient-to-r from-[#003820]/5 to-transparent mix-blend-multiply pointer-events-none"></div>
			<div class="container mx-auto px-4 relative z-10">
				<div class="max-w-5xl mx-auto">
					<div class="text-center mb-16">
						<div class="inline-flex items-center justify-center w-16 h-16 bg-[#003820]/10 rounded-full mb-6 animate-fade-in">
							<svg class="w-8 h-8 text-[#003820]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
							</svg>
						</div>
						<h2 class="text-4xl md:text-5xl font-bold mb-8 text-[#003820] animate-fade-in">
							Who We Are
						</h2>
					</div>
					<div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
						<div class="animate-slide-in-left">
							<div class="prose prose-lg text-[#3C4145]/80">
								<p class="text-xl leading-relaxed mb-6">
									Founded in 2017, 3Pay Global provides strategic litigation funding of between £250,000 and £3 million to claimants with strong legal cases but limited financial resources.
								</p>
								<p class="text-lg leading-relaxed mb-6">
									We support individuals and businesses pursuing justice against well-resourced defendants, including corporations, insurers, and negligent professionals.
								</p>
								<p class="text-lg leading-relaxed font-semibold text-[#003820]">
									Our role is to unlock high-value legal claims that would otherwise remain out of reach.
								</p>
							</div>
						</div>
						<div class="animate-slide-in-right">
							<div class="premium-card p-8 rounded-xl">
								<div class="text-center">
									<div class="w-20 h-20 bg-[#003820]/10 rounded-full flex items-center justify-center mx-auto mb-6">
										<svg class="w-10 h-10 text-[#003820]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
										</svg>
									</div>
									<h3 class="text-2xl font-bold text-[#003820] mb-4">Founded in 2017</h3>
									<p class="text-[#3C4145]/80">
										Providing strategic litigation funding with a proven track record of success
									</p>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</section>

		<!-- What Sets Us Apart Section -->
		<section class="py-24 bg-gray-50 relative overflow-hidden">
			<div class="absolute inset-0 bg-[#003820]/5 animate-rotate-slow"></div>
			<div class="container mx-auto px-4 relative z-10">
				<div class="max-w-5xl mx-auto">
					<div class="text-center mb-16">
						<div class="inline-flex items-center justify-center w-16 h-16 bg-[#003820]/10 rounded-full mb-6 animate-fade-in">
							<svg class="w-8 h-8 text-[#003820]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"/>
							</svg>
						</div>
						<h2 class="text-4xl md:text-5xl font-bold mb-8 text-[#003820] animate-fade-in">
							What Sets Us Apart
						</h2>
					</div>
					<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
						<div class="premium-card p-6 rounded-xl text-center animate-slide-in-left">
							<div class="w-16 h-16 bg-[#003820]/10 rounded-full flex items-center justify-center mx-auto mb-6">
								<svg class="w-8 h-8 text-[#003820]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
								</svg>
							</div>
							<h3 class="text-xl font-semibold mb-4 text-[#003820]">100% Success Rate</h3>
							<p class="text-[#3C4145]/80">A 100 percent success rate across all funded claims to date</p>
						</div>
						<div class="premium-card p-6 rounded-xl text-center animate-slide-in-up">
							<div class="w-16 h-16 bg-[#003820]/10 rounded-full flex items-center justify-center mx-auto mb-6">
								<svg class="w-8 h-8 text-[#003820]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"/>
								</svg>
							</div>
							<h3 class="text-xl font-semibold mb-4 text-[#003820]">Rigorous Due Diligence</h3>
							<p class="text-[#3C4145]/80">A rigorous due diligence process that filters out weak or speculative cases</p>
						</div>
						<div class="premium-card p-6 rounded-xl text-center animate-slide-in-down">
							<div class="w-16 h-16 bg-[#003820]/10 rounded-full flex items-center justify-center mx-auto mb-6">
								<svg class="w-8 h-8 text-[#003820]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
								</svg>
							</div>
							<h3 class="text-xl font-semibold mb-4 text-[#003820]">Strong Relationships</h3>
							<p class="text-[#3C4145]/80">Strong working relationships with top solicitors, barristers, insurance underwriters, and litigation experts</p>
						</div>
						<div class="premium-card p-6 rounded-xl text-center animate-slide-in-right">
							<div class="w-16 h-16 bg-[#003820]/10 rounded-full flex items-center justify-center mx-auto mb-6">
								<svg class="w-8 h-8 text-[#003820]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
								</svg>
							</div>
							<h3 class="text-xl font-semibold mb-4 text-[#003820]">Quality Focus</h3>
							<p class="text-[#3C4145]/80">A focus on quality, strategy, and disciplined capital deployment</p>
						</div>
					</div>
				</div>
			</div>
		</section>

		<!-- Founder Section -->
		<section class="py-24 bg-white relative overflow-hidden">
			<div class="absolute inset-0 bg-gradient-to-l from-[#003820]/5 to-transparent mix-blend-multiply pointer-events-none"></div>
			<div class="container mx-auto px-4 relative z-10">
				<div class="max-w-5xl mx-auto">
					<div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
						<div class="animate-slide-in-left">
							<div class="premium-card p-8 rounded-xl">
								<div class="text-center">
									<div class="w-24 h-24 bg-[#003820]/10 rounded-full flex items-center justify-center mx-auto mb-6">
										<svg class="w-12 h-12 text-[#003820]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
										</svg>
									</div>
									<h3 class="text-2xl font-bold text-[#003820] mb-4">Peter Murray</h3>
									<p class="text-lg text-[#3C4145]/80 mb-4">Founder & Managing Director</p>
									<p class="text-[#3C4145]/80">
										Independent insolvency professional and author with more than 30 years of experience
									</p>
								</div>
							</div>
						</div>
						<div class="animate-slide-in-right">
							<div class="prose prose-lg text-[#3C4145]/80">
								<p class="text-xl leading-relaxed mb-6">
									3Pay Global was founded by Peter Murray, an independent insolvency professional and author with more than 30 years of experience in commercial debt recovery, litigation, and both personal and corporate insolvency.
								</p>
								<p class="text-lg leading-relaxed">
									Peter's extensive background in the legal and financial sectors provides 3Pay Global with the expertise and insight needed to identify and support high-merit claims with the greatest potential for success.
								</p>
							</div>
						</div>
					</div>
				</div>
			</div>
		</section>

		<!-- Our Mission Section -->
		<section class="py-24 bg-gray-100 relative overflow-hidden">
			<div class="absolute inset-0 bg-[#003820]/5 animate-rotate-reverse-slow"></div>
			<div class="container mx-auto px-4 relative z-10">
				<div class="max-w-5xl mx-auto text-center">
					<div class="inline-flex items-center justify-center w-16 h-16 bg-[#003820]/10 rounded-full mb-6 animate-fade-in">
						<svg class="w-8 h-8 text-[#003820]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
						</svg>
					</div>
					<h2 class="text-4xl md:text-5xl font-bold mb-8 text-[#003820] animate-fade-in">
						Our Mission
					</h2>
					<div class="max-w-3xl mx-auto">
						<p class="text-3xl md:text-4xl font-bold text-[#003820] mb-8 animate-fade-in delay-100">
							We exist to fund justice.
						</p>
						<div class="prose prose-lg text-[#3C4145]/80">
							<p class="text-xl leading-relaxed animate-fade-in delay-200">
								3Pay Global is committed to empowering claimants who would otherwise be excluded from legal redress due to lack of funds. Our mission is to finance well-prepared litigants and help them access the legal system on equal terms, turning strong claims into successful outcomes for everyone involved.
							</p>
						</div>
					</div>
				</div>
			</div>
		</section>

		<!-- Become a Co-Funder Section -->
		<section class="py-24 bg-white relative overflow-hidden">
			<div class="absolute inset-0 bg-gradient-to-r from-[#003820]/5 to-transparent mix-blend-multiply pointer-events-none"></div>
			<div class="container mx-auto px-4 relative z-10">
				<div class="max-w-5xl mx-auto">
					<div class="text-center mb-16">
						<div class="inline-flex items-center justify-center w-16 h-16 bg-[#003820]/10 rounded-full mb-6 animate-fade-in">
							<svg class="w-8 h-8 text-[#003820]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
							</svg>
						</div>
						<h2 class="text-4xl md:text-5xl font-bold mb-8 text-[#003820] animate-fade-in">
							Become a 3Pay Co-Funder
						</h2>
						<p class="text-xl text-[#3C4145]/80 max-w-3xl mx-auto animate-fade-in delay-100">
							Unlock the potential for truly exceptional returns by becoming a 3Pay co-funder. Before funding a claim, you will first need to register as a subscriber—and the good news is, it's completely free.
						</p>
					</div>

					<!-- Step One -->
					<div class="mb-16">
						<div class="premium-card p-8 rounded-xl animate-slide-in-left">
							<div class="flex items-start">
								<div class="w-12 h-12 bg-[#003820]/10 rounded-full flex items-center justify-center mr-6 flex-shrink-0">
									<span class="text-xl font-bold text-[#003820]">1</span>
								</div>
								<div>
									<h3 class="text-2xl font-bold text-[#003820] mb-4">Step One: Become a Subscriber</h3>
									<p class="text-lg text-[#3C4145]/80 mb-4">
										Getting started is simple. Complete the quick and easy sign-up process, and you'll gain immediate access to exclusive resources designed to guide you through the litigation funding journey.
									</p>
									<div class="mt-6">
										<a href="/apply" class="submit-button inline-flex items-center justify-center px-6 py-3 text-lg font-semibold text-white rounded-xl hover:shadow-lg transition-all duration-500">
											<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"/>
											</svg>
											Sign Up Here
										</a>
									</div>
								</div>
							</div>
						</div>
					</div>

					<!-- What You'll Get -->
					<div class="mb-16">
						<h3 class="text-3xl font-bold text-[#003820] mb-8 text-center animate-fade-in">
							What You'll Get as a Subscriber
						</h3>
						<div class="premium-card p-8 rounded-xl animate-slide-in-right">
							<p class="text-lg text-[#3C4145]/80 mb-6">
								All subscribers receive free access to the 3Pay Knowledge Library, which includes blogs, podcasts, and expert insights on:
							</p>
							<div class="grid grid-cols-1 md:grid-cols-3 gap-6">
								<div class="flex items-start">
									<div class="w-8 h-8 bg-[#003820]/10 rounded-full flex items-center justify-center mr-3 flex-shrink-0 mt-1">
										<svg class="w-4 h-4 text-[#003820]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
										</svg>
									</div>
									<p class="text-[#3C4145]/80">The litigation funding landscape in England and Wales</p>
								</div>
								<div class="flex items-start">
									<div class="w-8 h-8 bg-[#003820]/10 rounded-full flex items-center justify-center mr-3 flex-shrink-0 mt-1">
										<svg class="w-4 h-4 text-[#003820]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
										</svg>
									</div>
									<p class="text-[#3C4145]/80">How the 3Pay business model works</p>
								</div>
								<div class="flex items-start">
									<div class="w-8 h-8 bg-[#003820]/10 rounded-full flex items-center justify-center mr-3 flex-shrink-0 mt-1">
										<svg class="w-4 h-4 text-[#003820]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
										</svg>
									</div>
									<p class="text-[#3C4145]/80">What to expect when co-funding with 3Pay</p>
								</div>
							</div>
						</div>
					</div>

					<!-- Subscriber Levels -->
					<div class="mb-16">
						<h3 class="text-3xl font-bold text-[#003820] mb-8 text-center animate-fade-in">
							Subscriber Levels: Learn Before You Fund
						</h3>
						<div class="premium-card p-8 rounded-xl animate-slide-in-left">
							<p class="text-lg text-[#3C4145]/80 mb-6">
								There are three subscriber levels: 1, 2, and 3. You'll progress through them by confirming that you've read or listened to 23 blogs or podcasts. It's fast, straightforward, and ensures you have a strong understanding of how litigation funding works and what makes the 3Pay approach unique.
							</p>
							<div class="bg-[#003820]/5 p-6 rounded-lg">
								<p class="text-lg font-semibold text-[#003820] mb-2">
									Unlock Access to Live Claims
								</p>
								<p class="text-[#3C4145]/80">
									Once you've completed all three levels, you'll unlock access to view live claims available for co-funding.
								</p>
							</div>
						</div>
					</div>

					<!-- Investment Profile -->
					<div class="mb-16">
						<h3 class="text-3xl font-bold text-[#003820] mb-8 text-center animate-fade-in">
							Understanding Your Investment Profile
						</h3>
						<div class="premium-card p-8 rounded-xl animate-slide-in-right">
							<p class="text-lg text-[#3C4145]/80 mb-6">
								Before co-funding a claim, we'll ask you to confirm a few basic details about your investment profile:
							</p>
							<div class="grid grid-cols-1 md:grid-cols-3 gap-6">
								<div class="bg-[#003820]/5 p-6 rounded-lg text-center">
									<div class="w-12 h-12 bg-[#003820]/10 rounded-full flex items-center justify-center mx-auto mb-4">
										<svg class="w-6 h-6 text-[#003820]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
										</svg>
									</div>
									<h4 class="text-lg font-semibold text-[#003820] mb-2">Risk Appetite</h4>
									<p class="text-[#3C4145]/80">Low, medium, or high risk tolerance</p>
								</div>
								<div class="bg-[#003820]/5 p-6 rounded-lg text-center">
									<div class="w-12 h-12 bg-[#003820]/10 rounded-full flex items-center justify-center mx-auto mb-4">
										<svg class="w-6 h-6 text-[#003820]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
										</svg>
									</div>
									<h4 class="text-lg font-semibold text-[#003820] mb-2">Investor Status</h4>
									<p class="text-[#3C4145]/80">Sophisticated investor or high net worth individual qualification</p>
								</div>
								<div class="bg-[#003820]/5 p-6 rounded-lg text-center">
									<div class="w-12 h-12 bg-[#003820]/10 rounded-full flex items-center justify-center mx-auto mb-4">
										<svg class="w-6 h-6 text-[#003820]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
										</svg>
									</div>
									<h4 class="text-lg font-semibold text-[#003820] mb-2">Capital Commitment</h4>
									<p class="text-[#3C4145]/80">Level of capital you may wish to commit for up to two years</p>
								</div>
							</div>
							<div class="mt-6 bg-[#003820]/10 p-6 rounded-lg">
								<p class="text-lg text-[#003820] font-semibold mb-2">
									Tailored Matching
								</p>
								<p class="text-[#3C4145]/80">
									This helps us align opportunities with your goals and ensure you are matched with the right claims.
								</p>
							</div>
						</div>
					</div>

					<!-- Call to Action -->
					<div class="text-center animate-slide-up">
						<div class="premium-card p-8 rounded-xl">
							<h3 class="text-2xl font-bold text-[#003820] mb-4">
								Ready to Get Started?
							</h3>
							<p class="text-lg text-[#3C4145]/80 mb-6">
								Join our community of co-funders and help us fund justice while earning exceptional returns.
							</p>
							<div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
								<a href="https://app.3payglobal.com/#/signin" target="_blank" class="submit-button inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-white rounded-xl hover:shadow-lg transition-all duration-500">
									<svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"/>
									</svg>
									Become a Co-Funder
								</a>
								<a href="mailto:<EMAIL>" class="secondary-button inline-flex items-center justify-center px-8 py-4 text-lg font-bold text-[#003820] bg-white border-2 border-[#003820] rounded-xl hover:bg-[#003820] hover:text-white transition-all duration-500 transform hover:scale-105 shadow-md">
									<svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
									</svg>
									Contact Us
								</a>
							</div>
						</div>
					</div>
				</div>
			</div>
		</section>
	</div>
</Layout>

<style>
	/* Premium grid background */
	.premium-card {
		transform-style: preserve-3d;
		perspective: 1000px;
		transition: all 0.6s cubic-bezier(0.23, 1, 0.32, 1);
		background: linear-gradient(
			135deg,
			rgba(255, 255, 255, 0.9),
			rgba(255, 255, 255, 0.7)
		);
		backdrop-filter: blur(10px);
		border: 1px solid rgba(0, 56, 32, 0.1);
		box-shadow: 0 4px 16px rgba(0, 56, 32, 0.1);
	}

	.premium-card:hover {
		transform: translateY(-5px);
		box-shadow:
			0 20px 40px -12px rgba(0, 56, 32, 0.25),
			0 0 0 1px rgba(0, 56, 32, 0.1);
	}

	.premium-card::before {
		content: '';
		position: absolute;
		inset: 0;
		border-radius: inherit;
		background: linear-gradient(
			135deg,
			rgba(0, 56, 32, 0.1) 0%,
			rgba(0, 56, 32, 0.05) 50%,
			rgba(0, 56, 32, 0) 100%
		);
		opacity: 0;
		transition: opacity 0.4s;
	}

	.premium-card:hover::before {
		opacity: 1;
	}

	.bg-grid {
		background-image: linear-gradient(rgba(0, 56, 32, 0.05) 1px, transparent 1px),
			linear-gradient(90deg, rgba(0, 56, 32, 0.05) 1px, transparent 1px);
		background-size: 32px 32px;
		background-position: center center;
	}

	.submit-button {
		background: linear-gradient(to right, #003820, #005732, #003820);
		background-size: 200% auto;
		transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
		box-shadow: 0 4px 12px rgba(0, 56, 32, 0.15);
	}

	.submit-button:hover {
		background-position: right center;
		transform: translateY(-2px);
		box-shadow: 0 8px 24px rgba(0, 56, 32, 0.2);
	}

	.submit-button:hover svg {
		transform: translateX(4px);
	}

	.secondary-button {
		transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
	}

	.secondary-button:hover {
		transform: translateY(-2px);
		box-shadow: 0 8px 24px rgba(0, 56, 32, 0.2);
	}

	/* Fade In Animation */
	.animate-fade-in {
		animation: fadeIn 0.75s ease-out forwards;
		opacity: 0;
	}

	@keyframes fadeIn {
		from { opacity: 0; transform: translateY(20px); }
		to { opacity: 1; transform: translateY(0); }
	}

	/* Slide Up Animation */
	.animate-slide-up {
		animation: slideUp 0.75s ease-out forwards;
		opacity: 0;
	}

	@keyframes slideUp {
		from { opacity: 0; transform: translateY(50px); }
		to { opacity: 1; transform: translateY(0); }
	}

	/* Slide In Left Animation */
	.animate-slide-in-left {
		animation: slideInLeft 0.75s ease-out forwards;
		opacity: 0;
	}

	@keyframes slideInLeft {
		from { opacity: 0; transform: translateX(-50px); }
		to { opacity: 1; transform: translateX(0); }
	}

	/* Slide In Right Animation */
	.animate-slide-in-right {
		animation: slideInRight 0.75s ease-out forwards;
		opacity: 0;
	}

	@keyframes slideInRight {
		from { opacity: 0; transform: translateX(50px); }
		to { opacity: 1; transform: translateX(0); }
	}

	/* Slide In Up Animation */
	.animate-slide-in-up {
		animation: slideInUp 0.75s ease-out forwards;
		opacity: 0;
	}

	@keyframes slideInUp {
		from { opacity: 0; transform: translateY(50px); }
		to { opacity: 1; transform: translateY(0); }
	}

	/* Slide In Down Animation */
	.animate-slide-in-down {
		animation: slideInDown 0.75s ease-out forwards;
		opacity: 0;
	}

	@keyframes slideInDown {
		from { opacity: 0; transform: translateY(-50px); }
		to { opacity: 1; transform: translateY(0); }
	}

	/* Animation Delays */
	.delay-100 { animation-delay: 0.1s; }
	.delay-200 { animation-delay: 0.2s; }
	.delay-300 { animation-delay: 0.3s; }
	.delay-400 { animation-delay: 0.4s; }

	/* Rotate Animation */
	.animate-rotate-slow {
		animation: rotate 20s linear infinite;
	}

	.animate-rotate-reverse-slow {
		animation: rotateReverse 20s linear infinite;
	}

	@keyframes rotate {
		from {
			transform: rotate(0deg);
		}
		to {
			transform: rotate(360deg);
		}
	}

	@keyframes rotateReverse {
		from {
			transform: rotate(360deg);
		}
		to {
			transform: rotate(0deg);
		}
	}

	/* Float Animation */
	.animate-float-slow {
		animation: float 6s ease-in-out infinite;
	}

	.animate-float-reverse {
		animation: floatReverse 8s ease-in-out infinite;
	}

	@keyframes float {
		0%, 100% {
			transform: translateY(0);
		}
		50% {
			transform: translateY(-16px);
		}
	}

	@keyframes floatReverse {
		0%, 100% {
			transform: translateY(0);
		}
		50% {
			transform: translateY(16px);
		}
	}

	/* Pulse Animation */
	.animate-pulse-slow {
		animation: pulseSlow 4s ease-in-out infinite;
	}

	@keyframes pulseSlow {
		0%, 100% {
			opacity: 0.6;
		}
		50% {
			opacity: 1;
		}
	}

	/* Enhanced gradient animation */
	.animate-gradient {
		background-size: 300% 300%;
		animation: gradientPosition 8s ease infinite;
	}

	@keyframes gradientPosition {
		0% { background-position: 0% 50%; }
		50% { background-position: 100% 50%; }
		100% { background-position: 0% 50%; }
	}
</style>