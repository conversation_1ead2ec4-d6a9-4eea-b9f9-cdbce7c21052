---
import Layout from '../layouts/Layout.astro';
---

<Layout title="Claimants - 3Pay Global">
	<div class="relative w-full overflow-hidden">
		<!-- Hero Section -->
		<section class="relative min-h-screen flex items-center justify-center py-20 bg-gradient-to-br from-[#f8fffe] via-white to-[#f0f7f4]/30">
			<!-- Enhanced background effects with reduced opacity for better text contrast -->
			<div class="absolute inset-0 overflow-hidden pointer-events-none">
				<div class="absolute top-0 left-1/4 w-[45rem] h-[45rem] bg-gradient-to-br from-[#003820]/6 to-[#003820]/2 rounded-full blur-[160px] mix-blend-soft-light animate-float-slow"></div>
				<div class="absolute bottom-0 right-1/4 w-[35rem] h-[35rem] bg-gradient-to-tr from-[#003820]/8 to-[#003820]/2 rounded-full blur-[128px] mix-blend-soft-light animate-float-reverse"></div>
				<div class="absolute inset-0 bg-grid opacity-[0.008] mix-blend-overlay"></div>
				<!-- Justice-themed floating elements -->
				<div class="absolute top-1/3 left-1/5 w-3 h-3 bg-[#003820]/15 rounded-full animate-float-particle"></div>
				<div class="absolute top-2/3 right-1/5 w-2 h-2 bg-[#003820]/20 rounded-full animate-float-particle-delayed"></div>
				<div class="absolute bottom-1/3 left-2/3 w-4 h-4 bg-[#003820]/12 rounded-full animate-float-particle-slow"></div>
			</div>

			<!-- Enhanced text contrast overlay -->
			<div class="absolute inset-0 bg-white/30 backdrop-blur-[0.5px] pointer-events-none"></div>

			<div class="container mx-auto px-4 relative z-20">
				<div class="max-w-6xl mx-auto">
					<!-- Enhanced header with badge -->
					<div class="text-center mb-12 mt-8">
						<div class="inline-flex items-center bg-gradient-to-r from-[#003820]/15 to-[#003820]/10 backdrop-blur-sm px-6 py-3 rounded-full hover:scale-105 transition-all duration-300 hover:shadow-lg mb-8 border border-[#003820]/20">
							<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" class="w-5 h-5 mr-3 text-[#003820]">
								<path d="M12 2L2 7l10 5 10-5-10-5z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
								<path d="M2 17l10 5 10-5M2 12l10 5 10-5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
							</svg>
							<span class="text-base font-semibold text-[#003820]">Legal Funding Specialists</span>
						</div>
						
						<!-- Enhanced main heading with better contrast -->
						<div class="relative mb-8">
							<h1 class="text-5xl md:text-7xl lg:text-8xl font-bold leading-tight relative z-10 text-[#001a0f] drop-shadow-sm">
								Access to Justice, Backed by Expertise
							</h1>
							<!-- Subtle background for text contrast -->
							<div class="absolute inset-0 bg-white/25 backdrop-blur-sm rounded-3xl -z-10 transform scale-110"></div>
						</div>
						
						<!-- Enhanced subtitle with better visibility -->
						<h2 class="text-3xl md:text-4xl lg:text-5xl font-semibold mb-12 text-[#002818] drop-shadow-sm">
							Level the Playing Field
						</h2>
					</div>

					<!-- Enhanced content layout with better contrast -->
					<div class="grid lg:grid-cols-2 gap-16 items-center">
						<div class="space-y-8">
							<!-- Enhanced text container with background for better readability -->
							<div class="bg-white/85 backdrop-blur-sm rounded-3xl p-8 shadow-lg border border-[#003820]/10">
								<div class="prose prose-lg text-[#1a2e23]">
									<p class="text-xl md:text-2xl leading-relaxed animate-fade-in mb-6 font-medium">
										At 3Pay Global, we specialise in providing funding for claims in key industries where our experience truly makes a difference. Whether you're pursuing a case of <span class="text-[#003820] font-bold">professional negligence</span> against solicitors, architects, auditors, or surveyors, or facing challenges in <span class="text-[#003820] font-bold">off-plan property disputes</span>, we are here to help you take on even the most well-funded opponents and achieve a successful outcome.
									</p>
								</div>
								
								<div class="flex flex-col sm:flex-row gap-4 animate-slide-up mt-8">
									<a href="#how-it-works" class="primary-button inline-flex items-center justify-center px-8 py-4 text-lg font-bold text-white rounded-2xl hover:shadow-2xl transition-all duration-500 transform hover:scale-105">
										Learn How It Works
										<svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"/>
										</svg>
									</a>
									<a href="mailto:<EMAIL>" class="secondary-button inline-flex items-center justify-center px-8 py-4 text-lg font-bold text-[#003820] bg-white border-2 border-[#003820] rounded-2xl hover:bg-[#003820] hover:text-white transition-all duration-500 transform hover:scale-105 shadow-md">
										<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
										</svg>
										Contact Us
									</a>
								</div>
							</div>
						</div>
						
						<!-- Enhanced visual element -->
						<div class="relative">
							<div class="bg-white/95 backdrop-blur-sm rounded-3xl p-8 shadow-2xl border border-[#003820]/15">
								<h3 class="text-2xl font-bold text-[#003820] mb-6 text-center">Our Specializations</h3>
								<div class="space-y-4">
									<div class="flex items-center space-x-4 p-4 bg-[#003820]/8 rounded-xl border border-[#003820]/10">
										<div class="w-10 h-10 bg-gradient-to-br from-[#003820] to-[#184E35] rounded-lg flex items-center justify-center flex-shrink-0">
											<svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
											</svg>
										</div>
										<span class="text-[#003820] font-semibold">Professional Negligence Claims</span>
									</div>
									<div class="flex items-center space-x-4 p-4 bg-[#003820]/8 rounded-xl border border-[#003820]/10">
										<div class="w-10 h-10 bg-gradient-to-br from-[#184E35] to-[#243255] rounded-lg flex items-center justify-center flex-shrink-0">
											<svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
											</svg>
										</div>
										<span class="text-[#003820] font-semibold">Off-Plan Property Disputes</span>
									</div>
									<div class="flex items-center space-x-4 p-4 bg-[#003820]/8 rounded-xl border border-[#003820]/10">
										<div class="w-10 h-10 bg-gradient-to-br from-[#243255] to-[#003820] rounded-lg flex items-center justify-center flex-shrink-0">
											<svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
											</svg>
										</div>
										<span class="text-[#003820] font-semibold">Up to £3M Funding Available</span>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="absolute bottom-0 left-0 w-full h-32 bg-gradient-to-t from-[#003820]/12 to-transparent pointer-events-none"></div>
		</section>

		<!-- Why Choose 3Pay? Section -->
		<section class="py-24 bg-gradient-to-br from-gray-50 to-white relative overflow-hidden">
			<div class="absolute inset-0 bg-[#003820]/3 animate-rotate-slow"></div>
			<div class="absolute top-0 right-0 w-96 h-96 bg-gradient-to-bl from-[#003820]/8 to-transparent rounded-full blur-3xl"></div>
			<div class="absolute bottom-0 left-0 w-72 h-72 bg-gradient-to-tr from-[#003820]/6 to-transparent rounded-full blur-2xl"></div>
			
			<div class="container mx-auto px-4 relative z-10">
				<div class="max-w-6xl mx-auto">
					<div class="text-center mb-16">
						<h2 class="text-4xl md:text-5xl lg:text-6xl font-bold mb-8 text-[#003820] animate-fade-in">
							Why Choose 3Pay?
						</h2>
					</div>
					
					<div class="grid grid-cols-1 md:grid-cols-3 gap-8">
						<!-- No Financial Risk Card -->
						<div class="benefit-card group relative">
							<div class="card-border absolute inset-0 rounded-3xl -z-10"></div>
							<div class="relative z-10 bg-white/95 rounded-3xl p-8 transition-all duration-500">
								<div class="text-center">
									<div class="w-16 h-16 bg-gradient-to-br from-[#003820] to-[#184E35] rounded-2xl flex items-center justify-center mx-auto mb-6 transform transition-all duration-300 group-hover:scale-110 group-hover:rotate-6">
										<svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"/>
										</svg>
									</div>
									<h3 class="text-2xl font-bold mb-4 text-[#003820]">No Financial Risk</h3>
									<p class="text-[#2a3f32] leading-relaxed font-medium">You can pursue your claim with full financial backing, knowing that if your case doesn't succeed, you owe us nothing.</p>
								</div>
							</div>
						</div>

						<!-- Expertise Card -->
						<div class="benefit-card group relative">
							<div class="card-border absolute inset-0 rounded-3xl -z-10"></div>
							<div class="relative z-10 bg-white/95 rounded-3xl p-8 transition-all duration-500">
								<div class="text-center">
									<div class="w-16 h-16 bg-gradient-to-br from-[#184E35] to-[#243255] rounded-2xl flex items-center justify-center mx-auto mb-6 transform transition-all duration-300 group-hover:scale-110 group-hover:rotate-6">
										<svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
										</svg>
									</div>
									<h3 class="text-2xl font-bold mb-4 text-[#003820]">Expertise in Your Industry</h3>
									<p class="text-[#2a3f32] leading-relaxed font-medium">We focus on the industries where we have deep knowledge and a proven track record of success.</p>
								</div>
							</div>
						</div>

						<!-- Full Funding Card -->
						<div class="benefit-card group relative">
							<div class="card-border absolute inset-0 rounded-3xl -z-10"></div>
							<div class="relative z-10 bg-white/95 rounded-3xl p-8 transition-all duration-500">
								<div class="text-center">
									<div class="w-16 h-16 bg-gradient-to-br from-[#243255] to-[#003820] rounded-2xl flex items-center justify-center mx-auto mb-6 transform transition-all duration-300 group-hover:scale-110 group-hover:rotate-6">
										<svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
										</svg>
									</div>
									<h3 class="text-2xl font-bold mb-4 text-[#003820]">Full Funding</h3>
									<p class="text-[#2a3f32] leading-relaxed font-medium">3Pay covers 100% of the pre-agreed legal disbursement costs—up to £3,000,000—ensuring you have everything you need to win.</p>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</section>

		<!-- How It Works Section -->
		<section id="how-it-works" class="py-24 bg-gradient-to-br from-white to-[#f0f7f4]/50 relative overflow-hidden">
			<div class="absolute inset-0 bg-gradient-to-r from-[#003820]/8 to-transparent mix-blend-multiply pointer-events-none"></div>
			<div class="container mx-auto px-4 relative z-10">
				<div class="max-w-6xl mx-auto">
					<div class="text-center mb-16">
						<h2 class="text-4xl md:text-5xl lg:text-6xl font-bold mb-8 text-[#003820] animate-fade-in">
							How It Works
						</h2>
					</div>
					
					<div class="grid md:grid-cols-2 gap-12 items-center">
						<div class="space-y-8">
							<div class="process-step">
								<div class="flex items-start space-x-4">
									<div class="w-12 h-12 bg-gradient-to-br from-[#003820] to-[#184E35] rounded-xl flex items-center justify-center flex-shrink-0 text-white font-bold text-lg">
										1
									</div>
									<div>
										<h3 class="text-xl font-bold text-[#003820] mb-2">Conditional Fee Arrangement</h3>
										<p class="text-[#2a3f32] font-medium">We work closely with you and your solicitor to agree on a conditional fee arrangement for legal fees.</p>
									</div>
								</div>
							</div>
							
							<div class="process-step">
								<div class="flex items-start space-x-4">
									<div class="w-12 h-12 bg-gradient-to-br from-[#184E35] to-[#243255] rounded-xl flex items-center justify-center flex-shrink-0 text-white font-bold text-lg">
										2
									</div>
									<div>
										<h3 class="text-xl font-bold text-[#003820] mb-2">Full Funding Provided</h3>
										<p class="text-[#2a3f32] font-medium">3Pay provides full funding for the legal disbursement costs, with no upfront costs to you.</p>
									</div>
								</div>
							</div>
							
							<div class="process-step">
								<div class="flex items-start space-x-4">
									<div class="w-12 h-12 bg-gradient-to-br from-[#243255] to-[#003820] rounded-xl flex items-center justify-center flex-shrink-0 text-white font-bold text-lg">
										3
									</div>
									<div>
										<h3 class="text-xl font-bold text-[#003820] mb-2">Success-Based Repayment</h3>
										<p class="text-[#2a3f32] font-medium">We are only repaid when your claim is successful. If you lose the case, you don't owe us a penny.</p>
									</div>
								</div>
							</div>
						</div>
						
						<!-- Visual process flow -->
						<div class="relative">
							<div class="bg-white/95 backdrop-blur-sm rounded-3xl p-8 shadow-2xl border border-[#003820]/10">
								<h3 class="text-2xl font-bold text-[#003820] mb-8 text-center">Simple Process</h3>
								<div class="space-y-6">
									<div class="flex items-center justify-between">
										<span class="text-[#003820] font-semibold">Initial Consultation</span>
										<div class="w-8 h-8 bg-[#003820] rounded-full flex items-center justify-center">
											<svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
											</svg>
										</div>
									</div>
									<div class="w-full h-px bg-[#003820]/20"></div>
									<div class="flex items-center justify-between">
										<span class="text-[#003820] font-semibold">Case Assessment</span>
										<div class="w-8 h-8 bg-[#003820] rounded-full flex items-center justify-center">
											<svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
											</svg>
										</div>
									</div>
									<div class="w-full h-px bg-[#003820]/20"></div>
									<div class="flex items-center justify-between">
										<span class="text-[#003820] font-semibold">Funding Agreement</span>
										<div class="w-8 h-8 bg-[#003820] rounded-full flex items-center justify-center">
											<svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
											</svg>
										</div>
									</div>
									<div class="w-full h-px bg-[#003820]/20"></div>
									<div class="flex items-center justify-between">
										<span class="text-[#003820] font-semibold">Case Proceeds</span>
										<div class="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center">
											<svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"/>
											</svg>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</section>

		<!-- Successful Claims Section -->
		<section class="py-24 bg-gradient-to-br from-gray-100 to-gray-50 relative overflow-hidden">
			<div class="absolute inset-0 bg-[#003820]/3 animate-rotate-reverse-slow"></div>
			<div class="container mx-auto px-4 relative z-10">
				<div class="max-w-6xl mx-auto">
					<div class="text-center mb-16">
						<h2 class="text-4xl md:text-5xl lg:text-6xl font-bold mb-8 text-[#003820] animate-fade-in">
							Successful Claims
						</h2>
					</div>

					<div class="grid lg:grid-cols-2 gap-16 items-center">
						<div class="space-y-8">
							<div class="bg-white/90 backdrop-blur-sm rounded-3xl p-8 shadow-lg border border-[#003820]/10">
								<div class="prose prose-lg text-[#1a2e23]">
									<p class="text-xl leading-relaxed mb-8 font-medium">
										When your claim succeeds, you will receive the net surplus after deducting:
									</p>

									<div class="space-y-4 mb-8">
										<div class="flex items-center space-x-4 p-4 bg-[#003820]/8 rounded-xl border border-[#003820]/10">
											<div class="w-8 h-8 bg-[#003820] rounded-full flex items-center justify-center flex-shrink-0">
												<svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
													<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
												</svg>
											</div>
											<span class="text-[#003820] font-semibold">3Pay's funding facility plus interest</span>
										</div>
										<div class="flex items-center space-x-4 p-4 bg-[#003820]/8 rounded-xl border border-[#003820]/10">
											<div class="w-8 h-8 bg-[#003820] rounded-full flex items-center justify-center flex-shrink-0">
												<svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
													<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
												</svg>
											</div>
											<span class="text-[#003820] font-semibold">Solicitors' fees</span>
										</div>
									</div>

									<p class="text-lg leading-relaxed font-medium text-[#2a3f32]">
										Our specialisation in high-merit claims means we only take on cases that have the potential for strong results. By partnering with 3Pay, you gain access to financial support and legal resources that are typically reserved for large corporations or institutions, giving you the edge in your legal battle.
									</p>
								</div>
							</div>
						</div>

						<!-- Success metrics -->
						<div class="relative">
							<div class="bg-gradient-to-br from-[#003820]/8 to-[#003820]/12 rounded-3xl p-8 border border-[#003820]/15">
								<h3 class="text-2xl font-bold text-[#003820] mb-8 text-center">Why We Win</h3>
								<div class="grid grid-cols-2 gap-6">
									<div class="text-center">
										<div class="text-4xl font-bold text-[#003820] mb-2">100%</div>
										<div class="text-sm text-[#2a3f32] font-medium">Success Rate</div>
									</div>
									<div class="text-center">
										<div class="text-4xl font-bold text-[#003820] mb-2">£3M</div>
										<div class="text-sm text-[#2a3f32] font-medium">Max Funding</div>
									</div>
									<div class="text-center">
										<div class="text-4xl font-bold text-[#003820] mb-2">8+</div>
										<div class="text-sm text-[#2a3f32] font-medium">Years Experience</div>
									</div>
									<div class="text-center">
										<div class="text-4xl font-bold text-[#003820] mb-2">0%</div>
										<div class="text-sm text-[#2a3f32] font-medium">Upfront Cost</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</section>

		<!-- Testimonial Section -->
		<section class="py-24 bg-gradient-to-br from-white to-[#f0f7f4] relative overflow-hidden">
			<div class="absolute top-0 right-0 w-96 h-96 rounded-full bg-[#003820]/8 filter blur-3xl animate-float-slow"></div>
			<div class="absolute bottom-0 left-0 w-72 h-72 rounded-full bg-[#003820]/6 filter blur-2xl animate-float-reverse"></div>

			<div class="container mx-auto px-4 relative z-10">
				<div class="max-w-5xl mx-auto">
					<div class="text-center mb-16">
						<h2 class="text-4xl md:text-5xl lg:text-6xl font-bold mb-8 text-[#003820] animate-fade-in">
							Don't Just Take Our Word for It
						</h2>
					</div>

					<!-- Enhanced testimonial card -->
					<div class="relative">
						<div class="bg-white/95 backdrop-blur-sm rounded-3xl p-12 shadow-2xl border border-[#003820]/10 max-w-4xl mx-auto">
							<div class="text-center">
								<!-- Quote icon -->
								<div class="w-16 h-16 bg-gradient-to-br from-[#003820] to-[#184E35] rounded-full flex items-center justify-center mx-auto mb-8">
									<svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
										<path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-10zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h4v10h-10z"/>
									</svg>
								</div>

								<blockquote class="text-xl md:text-2xl text-[#1a2e23] leading-relaxed mb-8 italic font-medium">
									"Thanks to 3Pay's support, I was able to pursue a claim that I otherwise couldn't have afforded. The funding process was straightforward, and I felt fully backed throughout the entire case."
								</blockquote>

								<div class="flex items-center justify-center space-x-4">
									<div class="w-12 h-12 bg-gradient-to-br from-[#003820] to-[#184E35] rounded-full flex items-center justify-center">
										<svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
										</svg>
									</div>
									<div class="text-left">
										<div class="font-bold text-[#003820]">Successful Claimant</div>
										<div class="text-sm text-[#2a3f32] font-medium">Professional Negligence Sector</div>
									</div>
								</div>
							</div>
						</div>
					</div>

					<!-- Call to action -->
					<div class="text-center mt-16">
						<div class="flex flex-col sm:flex-row gap-6 justify-center items-center animate-slide-up">
							<a href="mailto:<EMAIL>" class="primary-button inline-flex items-center justify-center px-10 py-5 text-lg font-bold text-white rounded-2xl hover:shadow-2xl transition-all duration-500 transform hover:scale-105">
								<svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
								</svg>
								Get in Touch
							</a>
							<a href="tel:+44 (2)076 9289 77" class="secondary-button inline-flex items-center justify-center px-10 py-5 text-lg font-bold text-[#003820] bg-white border-2 border-[#003820] rounded-2xl hover:bg-[#003820] hover:text-white transition-all duration-500 transform hover:scale-105 shadow-md">
								<svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
								</svg>
								Call Us
							</a>
						</div>
					</div>
				</div>
			</div>
		</section>
	</div>
</Layout>

<style>
	/* Premium grid background */
	.bg-grid {
		background-image: linear-gradient(rgba(0, 56, 32, 0.05) 1px, transparent 1px),
			linear-gradient(90deg, rgba(0, 56, 32, 0.05) 1px, transparent 1px);
		background-size: 32px 32px;
		background-position: center center;
	}

	/* Enhanced text contrast and readability */
	.hero-title-shadow {
		text-shadow: 0 2px 4px rgba(255, 255, 255, 0.8), 0 1px 2px rgba(0, 56, 32, 0.1);
	}

	/* Enhanced button styles with better contrast */
	.primary-button {
		background: linear-gradient(135deg, #003820, #005732, #184E35);
		transition: all 0.3s ease;
		box-shadow: 0 4px 16px rgba(0, 56, 32, 0.3);
	}

	.primary-button:hover {
		transform: translateY(-2px);
		box-shadow: 0 8px 24px rgba(0, 56, 32, 0.4);
	}

	.primary-button:hover svg {
		transform: translateX(4px);
	}

	.secondary-button {
		transition: all 0.3s ease;
		box-shadow: 0 2px 8px rgba(0, 56, 32, 0.15);
	}

	.secondary-button:hover {
		box-shadow: 0 4px 16px rgba(0, 56, 32, 0.25);
		transform: translateY(-1px);
	}

	/* Benefit cards with enhanced effects and better contrast */
	.benefit-card {
		transition: all 0.3s ease;
		border-radius: 1.5rem;
	}

	.card-border {
		background: linear-gradient(115deg, #003820, #184E35, #003820);
		opacity: 0.2;
		transition: all 0.3s ease;
		filter: blur(8px);
	}

	.benefit-card:hover {
		transform: translateY(-4px);
		box-shadow: 0 12px 24px rgba(0, 56, 32, 0.2);
	}

	.benefit-card:hover .card-border {
		opacity: 0.3;
	}

	/* Simplified animations */
	.animate-fade-in {
		animation: fadeIn 0.6s ease-out;
	}

	@keyframes fadeIn {
		from {
			opacity: 0;
			transform: translateY(20px);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}

	.animate-slide-up {
		animation: slideUp 0.6s ease-out;
	}

	@keyframes slideUp {
		from {
			opacity: 0;
			transform: translateY(30px);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}

	/* Simplified floating animations */
	.animate-float-slow {
		animation: float 6s ease-in-out infinite;
	}

	.animate-float-reverse {
		animation: floatReverse 8s ease-in-out infinite;
	}

	.animate-float-particle {
		animation: floatParticle 4s ease-in-out infinite;
	}

	.animate-float-particle-delayed {
		animation: floatParticle 5s ease-in-out infinite;
		animation-delay: 1s;
	}

	.animate-float-particle-slow {
		animation: floatParticle 6s ease-in-out infinite;
		animation-delay: 2s;
	}

	@keyframes float {
		0%, 100% { transform: translateY(0); }
		50% { transform: translateY(-10px); }
	}

	@keyframes floatReverse {
		0%, 100% { transform: translateY(0); }
		50% { transform: translateY(10px); }
	}

	@keyframes floatParticle {
		0%, 100% { transform: translateY(0); opacity: 0.3; }
		50% { transform: translateY(-15px); opacity: 0.6; }
	}

	/* Simplified rotation animations */
	.animate-rotate-slow {
		animation: rotate 20s linear infinite;
	}

	.animate-rotate-reverse-slow {
		animation: rotateReverse 15s linear infinite;
	}

	@keyframes rotate {
		from { transform: rotate(0deg); }
		to { transform: rotate(360deg); }
	}

	@keyframes rotateReverse {
		from { transform: rotate(0deg); }
		to { transform: rotate(-360deg); }
	}

	/* Process steps enhancement with better hover effects */
	.process-step {
		transition: all 0.3s ease;
		padding: 1rem;
		border-radius: 1rem;
	}

	.process-step:hover {
		transform: translateX(10px);
		background-color: rgba(0, 56, 32, 0.05);
	}

	/* Enhanced responsive design */
	@media (max-width: 768px) {
		.primary-button, .secondary-button {
			width: 100%;
			max-width: 320px;
			padding: 1rem 2rem;
		}

		.benefit-card {
			margin-bottom: 2rem;
		}

		.hero-title-shadow {
			font-size: 2.5rem !important;
			line-height: 1.2;
		}
	}

	@media (max-width: 640px) {
		.hero-title-shadow {
			font-size: 2rem !important;
		}
	}

	/* Reduced motion support */
	@media (prefers-reduced-motion: reduce) {
		.animate-float-slow,
		.animate-float-reverse,
		.animate-float-particle,
		.animate-float-particle-delayed,
		.animate-float-particle-slow,
		.animate-rotate-slow,
		.animate-rotate-reverse-slow {
			animation: none;
		}

		.animate-fade-in,
		.animate-slide-up {
			animation: none;
			opacity: 1;
			transform: none;
		}
	}

	/* Fallback styles for better compatibility */
	.primary-button {
		background-color: #003820;
	}

	.secondary-button {
		background-color: white;
		color: #003820;
		border: 2px solid #003820;
	}

	.benefit-card {
		background-color: white;
		border: 1px solid rgba(0, 56, 32, 0.1);
	}

	/* Ensure text is always readable */
	.text-contrast {
		color: #1a2e23;
	}

	.bg-contrast {
		background-color: rgba(255, 255, 255, 0.9);
	}
</style>
