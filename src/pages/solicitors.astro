---
import Layout from '../layouts/Layout.astro';
import '@styles/animations.css';
---

<Layout title="Solicitors - 3Pay Global">
	<div class="relative w-full overflow-hidden">
		<!-- Hero Section -->
		<section class="relative min-h-screen flex items-center justify-center py-20 bg-[#003820]/5">
			<!-- Premium background effects -->
			<div class="absolute inset-0 overflow-hidden pointer-events-none">
				<div class="absolute top-0 left-1/4 w-[45rem] h-[45rem] bg-gradient-to-br from-[#003820]/10 to-[#003820]/5 rounded-full blur-[160px] mix-blend-soft-light animate-float-slow"></div>
				<div class="absolute bottom-0 right-1/4 w-[35rem] h-[35rem] bg-gradient-to-tr from-[#003820]/15 to-[#003820]/5 rounded-full blur-[128px] mix-blend-soft-light animate-float-reverse"></div>
				<div class="absolute inset-0 bg-grid opacity-[0.015] mix-blend-overlay"></div>
				<div class="absolute inset-0 pointer-events-none overflow-hidden">
					<div class="absolute top-1/4 left-1/4 w-64 h-64 bg-[#003820]/10 rounded-full filter blur-[100px] animate-pulse-slow"></div>
					<div class="absolute bottom-1/4 right-1/4 w-96 h-96 bg-[#003820]/5 rounded-full filter blur-[120px] animate-pulse-slow delay-1000"></div>
					<div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] bg-[#003820]/5 rounded-full filter blur-[150px] animate-pulse-slow delay-2000"></div>
				</div>
			</div>

			<div class="container mx-auto px-4 relative z-10">
				<div class="max-w-4xl mx-auto text-center">
					<h1 class="text-5xl md:text-7xl font-bold mb-6 animate-gradient bg-[length:200%_auto] bg-clip-text text-transparent bg-gradient-to-r from-[#003820] via-[#005732] to-[#003820]">
						Empower Your Clients With Risk-Free Funding
					</h1>
					<h2 class="text-3xl md:text-4xl font-semibold mb-8 text-[#003820] animate-pulse">
						Level the Playing Field for Justice
					</h2>
					<div class="prose prose-lg max-w-3xl mx-auto text-[#3C4145]/80">
						<p class="mb-8 animate-fade-in">
							If your client has a strong, compelling claim but lacks the financial resources to litigate, 3Pay Global can step in to provide the funding needed to pursue justice. Our tailored litigation funding service is designed to back high-merit claims in industries where we’ve already proven success.
						</p>
						<p class="mb-8 animate-fade-in delay-100">
							We’ll review the claim details and, if eligible, provide the financial backing necessary for your client to take on well-funded defendants without the burden of upfront costs. With 3Pay, you gain access to the funding resources that empower your clients to act when others might be stuck—an opportunity few are privileged to access.
						</p>
					</div>
				</div>
			</div>
			<div class="absolute bottom-0 left-0 w-full h-32 bg-gradient-to-t from-[#003820]/20 to-transparent pointer-events-none"></div>
		</section>

		<!-- What's Covered by 3Pay's Funding? -->
		<section class="py-24 bg-gray-50 relative overflow-hidden">
			<div class="absolute inset-0 bg-[#003820]/5 animate-rotate-slow"></div>
			<div class="container mx-auto px-4 relative z-10">
				<div class="max-w-5xl mx-auto text-center">
					<h2 class="text-4xl md:text-5xl font-bold mb-8 text-[#003820] animate-fade-in">
						What's Covered by 3Pay's Funding?
					</h2>
					<div class="prose prose-lg max-w-3xl mx-auto text-[#3C4145]/80">
						<ul class="list-none pl-5 mb-8">
							<li class="animate-fade-in delay-100 flex items-start">
								<svg class="w-6 h-6 mr-2 text-[#005732] flex-shrink-0 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
								</svg>
								<div>Legal Disbursements: Expert fees, court fees, counsel fees, and ATE insurance</div>
							</li>
							<li class="animate-fade-in delay-200 flex items-start">
								<svg class="w-6 h-6 mr-2 text-[#005732] flex-shrink-0 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
								</svg>
								<div>Up to £3,000,000: Our funding facility can cover the full scope of your client’s claim, with no obligation to repay unless successful</div>
							</li>
						</ul>
					</div>
				</div>
			</div>
		</section>

		<!-- How We Work -->
		<section class="py-24 bg-white relative overflow-hidden">
			<div class="absolute inset-0 bg-gradient-to-r from-[#003820]/10 to-transparent mix-blend-multiply pointer-events-none"></div>
			<div class="container mx-auto px-4 relative z-10">
				<div class="max-w-5xl mx-auto text-center">
					<h2 class="text-4xl md:text-5xl font-bold mb-8 text-[#003820] animate-fade-in">
						How We Work
					</h2>
					<div class="prose prose-lg max-w-3xl mx-auto text-[#3C4145]/80">
						<p class="mb-4 animate-fade-in delay-100">For your client’s claim to be eligible, we require:</p>
						<ul class="list-none pl-5 mb-8">
							<li class="animate-fade-in delay-200 flex items-start">
								<svg class="w-6 h-6 mr-2 text-[#005732] flex-shrink-0 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
								</svg>
								<div>A Conditional Fee Agreement between your firm and the client for solicitor fees</div>
							</li>
							<li class="animate-fade-in delay-300 flex items-start">
								<svg class="w-6 h-6 mr-2 text-[#005732] flex-shrink-0 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
								</svg>
								<div>Approval from your firm’s risk committee</div>
							</li>
							<li class="animate-fade-in delay-400 flex items-start">
								<svg class="w-6 h-6 mr-2 text-[#005732] flex-shrink-0 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
								</svg>
								<div>A Legal Opinion from experienced counsel with a history of handling similar claims</div>
							</li>
						</ul>
					</div>
				</div>
			</div>
		</section>

		<!-- Why Choose 3Pay? -->
		<section class="py-24 bg-gray-100 relative overflow-hidden">
			<div class="absolute inset-0 bg-[#003820]/5 animate-rotate-reverse-slow"></div>
			<div class="container mx-auto px-4 relative z-10">
				<div class="max-w-5xl mx-auto">
					<h2 class="text-4xl md:text-5xl font-bold mb-8 text-[#003820] text-center animate-fade-in">
						Why Choose 3Pay?
					</h2>
					<div class="grid grid-cols-1 md:grid-cols-2 gap-8">
						<div class="premium-card transform-3d hover-scale-105 animate-slide-in-left p-8 md:p-10 rounded-xl" data-aos="fade-up">
							<div class="premium-card-inner">
								<div class="w-16 h-16 icon-container flex items-center justify-center mb-6 float-element group">
									<svg class="w-8 h-8 text-[#003820] transform transition-transform group-hover:scale-110 duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 14l6-6m-5.5.5h.01m4.99 5h.01M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0H5m14 0a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2m14-8H5M12 3v3m0 0v3m0-3h3m-3 0H9"/>
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 8v1m0 3v1m0 3v1"/>
									</svg>
								</div>
								<h3 class="text-xl font-semibold mb-4 premium-text">Non-Recourse Funding</h3>
								<p class="text-[#3C4145]/80">Our funding is non-recourse, meaning you and your client are under no financial obligation if the claim is unsuccessful.</p>
							</div>
						</div>
						<div class="premium-card transform-3d hover-scale-105 animate-slide-in-right p-8 md:p-10 rounded-xl" data-aos="fade-up">
							<div class="premium-card-inner">
								<div class="w-16 h-16 icon-container flex items-center justify-center mb-6 float-element group">
									<svg class="w-8 h-8 text-[#003820] transform transition-transform group-hover:scale-110 duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M13 10V3L4 14h7v7l9-11h-7z"/>
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 8v4m0 4v.01M12 3v3"/>
									</svg>
								</div>
								<h3 class="text-xl font-semibold mb-4 premium-text">Quick & Hassle-Free</h3>
								<p class="text-[#3C4145]/80">We understand the urgency of litigation. Our streamlined process ensures that funding is in place swiftly, allowing you to focus on pursuing justice for your client.</p>
							</div>
						</div>
						<div class="premium-card transform-3d hover-scale-105 animate-slide-in-left mt-8 p-8 md:p-10 rounded-xl" data-aos="fade-up">
							<div class="premium-card-inner">
								<div class="w-16 h-16 icon-container flex items-center justify-center mb-6 float-element group">
									<svg class="w-8 h-8 text-[#003820] transform transition-transform group-hover:scale-110 duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"/>
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 9v2m0 4h.01"/>
									</svg>
								</div>
								<h3 class="text-xl font-semibold mb-4 premium-text">Risk-Free Opportunity</h3>
								<p class="text-[#3C4145]/80">With 3Pay, there is no financial risk—just a greater chance of achieving a successful outcome for your client.</p>
							</div>
						</div>
						<div class="premium-card transform-3d hover-scale-105 animate-slide-in-right mt-8 p-8 md:p-10 rounded-xl" data-aos="fade-up">
							<div class="premium-card-inner">
								<div class="w-16 h-16 icon-container flex items-center justify-center mb-6 float-element group">
									<svg class="w-8 h-8 text-[#003820] transform transition-transform group-hover:scale-110 duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 14v2m-4 0h8"/>
									</svg>
								</div>
								<h3 class="text-xl font-semibold mb-4 premium-text">A Trusted Partner</h3>
								<p class="text-[#3C4145]/80">We work closely with your firm, integrating our funding seamlessly into your legal strategy. We’re here to support you every step of the way.</p>
							</div>
						</div>
					</div>
				</div>
			</div>
		</section>

		<!-- Expertise in High-Merit Claims -->
		<section class="py-24 bg-white relative overflow-hidden">
			<div class="container mx-auto px-4">
				<div class="max-w-5xl mx-auto text-center">
					<h2 class="text-4xl md:text-5xl font-bold mb-8 text-[#003820] animate-fade-in">
						Expertise in High-Merit Claims
					</h2>
					<div class="prose prose-lg max-w-3xl mx-auto text-[#3C4145]/80">
						<p class="mb-8 animate-fade-in delay-100">
							3Pay specialises in claims related to professional negligence (solicitors, architects, auditors, surveyors), off-plan property, and other key sectors. Our team understands these areas intimately, ensuring we only back claims with the highest chance of success.
						</p>
					</div>
				</div>
			</div>
		</section>

		<!-- Schedule a Consultation With Our Team -->
		<section class="py-24 bg-gray-100 relative overflow-hidden">
			<div class="container mx-auto px-4">
				<div class="max-w-5xl mx-auto text-center">
					<h2 class="text-4xl md:text-5xl font-bold mb-8 text-[#003820] animate-fade-in">
						Schedule a Consultation With Our Team
					</h2>
					<div class="prose prose-lg max-w-3xl mx-auto text-[#3C4145]/80">
						<p class="mb-8 animate-fade-in delay-100">
							Don’t miss the opportunity to offer your client a risk-free chance to pursue justice. Get in touch now to find out how 3Pay can help fund your client’s claim and level the playing field against well-funded defendants.
						</p>
					</div>
					<div class="mt-12 animate-slide-up">
						<a href="mailto:<EMAIL>" class="submit-button inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-white rounded-xl hover:shadow-lg transition-all duration-500">
							Schedule a Consultation
							<svg class="w-5 h-5 ml-2 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.042A8.967 8.967 0 006 3.411m6 2.631A8.969 8.969 0 0118 3.411m-6 2.631v10.833c0 .722-.492 1.33-.584 1.679m0-1.679l3.176.588m-3.176-.588l-3.176.588"/>
							</svg>
						</a>
					</div>
				</div>
			</div>
			<div class="absolute top-0 right-0 w-48 h-48 rounded-full bg-[#003820]/10 filter blur-xl animate-float-slow"></div>
		</section>
	</div>
</Layout>

<style>
	/* Premium grid background */
	.premium-card {
		transform-style: preserve-3d;
		perspective: 1000px;
		transition: all 0.6s cubic-bezier(0.23, 1, 0.32, 1);
		background: linear-gradient(
			135deg,
			rgba(255, 255, 255, 0.9),
			rgba(255, 255, 255, 0.7)
		);
		backdrop-filter: blur(10px);
	}

	.premium-card:hover {
		transform: translateY(-10px) rotateX(5deg) rotateY(5deg);
		box-shadow:
			0 25px 50px -12px rgba(0, 56, 32, 0.25),
			0 0 0 1px rgba(0, 56, 32, 0.1);
	}

	.premium-card::before {
		content: '';
		position: absolute;
		inset: 0;
		border-radius: inherit;
		background: linear-gradient(
			135deg,
			rgba(0, 56, 32, 0.1) 0%,
			rgba(0, 56, 32, 0.05) 50%,
			rgba(0, 56, 32, 0) 100%
		);
		opacity: 0;
		transition: opacity 0.4s;
	}

	.premium-card:hover::before {
		opacity: 1;
	}

	.premium-text {
		background: linear-gradient(
			90deg,
			#003820,
			#005732,
			#003820,
			#004a28,
			#003820
		);
		background-size: 300% 100%;
		animation: gradientFlow 8s ease infinite;
		-webkit-background-clip: text;
		background-clip: text;
		color: transparent;
	}

	@keyframes gradientFlow {
		0% { background-position: 0% 50%; }
		50% { background-position: 100% 50%; }
		100% { background-position: 0% 50%; }
	}

	.float-element {
		animation: floatAnimation 6s ease-in-out infinite;
	}

	@keyframes floatAnimation {
		0%, 100% { transform: translateY(0) rotate(0deg); }
		25% { transform: translateY(-10px) rotate(2deg); }
		75% { transform: translateY(10px) rotate(-2deg); }
	}

	.glass-effect {
		background: rgba(255, 255, 255, 0.7);
		backdrop-filter: blur(10px);
		border: 1px solid rgba(0, 56, 32, 0.1);
		box-shadow:
			0 8px 32px rgba(0, 56, 32, 0.1),
			inset 0 0 0 1px rgba(255, 255, 255, 0.5);
	}

	.bg-grid {
		background-image: linear-gradient(rgba(0, 56, 32, 0.05) 1px, transparent 1px),
			linear-gradient(90deg, rgba(0, 56, 32, 0.05) 1px, transparent 1px);
		background-size: 32px 32px;
		background-position: center center;
	}

	.submit-button {
		background: linear-gradient(to right, #003820, #005732, #003820);
		background-size: 200% auto;
		transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
		box-shadow: 0 4px 12px rgba(0, 56, 32, 0.15);
	}

	.submit-button:hover {
		background-position: right center;
		transform: translateY(-2px);
		box-shadow: 0 8px 24px rgba(0, 56, 32, 0.2);
	}

	.submit-button:hover svg {
		transform: translateX(4px);
	}

	/* Fade In Animation */
	.animate-fade-in {
		animation: fadeIn 0.75s ease-out forwards;
		opacity: 0;
	}

	@keyframes fadeIn {
		from { opacity: 0; transform: translateY(20px); }
		to { opacity: 1; transform: translateY(0); }
	}

	/* Slide Up Animation */
	.animate-slide-up {
		animation: slideUp 0.75s ease-out forwards;
		opacity: 0;
	}

	@keyframes slideUp {
		from { opacity: 0; transform: translateY(50px); }
		to { opacity: 1; transform: translateY(0); }
	}

	/* Slide In Left Animation */
	.animate-slide-in-left {
		animation: slideInLeft 0.75s ease-out forwards;
		opacity: 0;
	}

	@keyframes slideInLeft {
		from { opacity: 0; transform: translateX(-50px); }
		to { opacity: 1; transform: translateX(0); }
	}

	/* Slide In Right Animation */
	.animate-slide-in-right {
		animation: slideInRight 0.75s ease-out forwards;
		opacity: 0;
	}

	@keyframes slideInRight {
		from { opacity: 0; transform: translateX(50px); }
		to { opacity: 1; transform: translateX(0); }
	}

	/* Animation Delays */
	.delay-100 { animation-delay: 0.1s; }
	.delay-200 { animation-delay: 0.2s; }
	.delay-300 { animation-delay: 0.3s; }
	.delay-400 { animation-delay: 0.4s; }

	/* Pulse Animation */
	.animate-pulse {
		animation: pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
	}

	@keyframes pulse {
		0%, 100% {
			opacity: 1;
		}
		50% {
			opacity: 0.75;
		}
	}

	/* Rotate Animation */
	.animate-rotate-slow {
		animation: rotate 20s linear infinite;
	}

	.animate-rotate-reverse-slow {
		animation: rotateReverse 20s linear infinite;
	}

	@keyframes rotate {
		from {
			transform: rotate(0deg);
		}
		to {
			transform: rotate(360deg);
		}
	}

	@keyframes rotateReverse {
		from {
			transform: rotate(360deg);
		}
		to {
			transform: rotate(0deg);
		}
	}

	/* Float Animation */
	.animate-float-slow {
		animation: float 6s ease-in-out infinite;
	}

	@keyframes float {
		0%, 100% {
			transform: translateY(0);
		}
		50% {
			transform: translateY(-16px);
		}
	}

	/* Icon container styles with enhanced 3D effects */
	.icon-container {
		position: relative;
		overflow: hidden;
		border-radius: 1rem;
		background: linear-gradient(135deg, rgba(0, 56, 32, 0.1), rgba(0, 87, 50, 0.05));
		transition: all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
		transform-style: preserve-3d;
		perspective: 1000px;
		transform: translateZ(0) rotateX(0) rotateY(0);
		backface-visibility: hidden;
	}

	.icon-container::before {
		content: '';
		position: absolute;
		inset: 0;
		background: radial-gradient(
			circle at center,
			rgba(0, 56, 32, 0.2),
			transparent 70%
		);
		opacity: 0;
		transition: all 0.5s ease;
		transform: translateZ(-10px);
	}

	.icon-container:hover {
		transform: translateZ(20px) rotateX(10deg) rotateY(10deg);
		box-shadow: 
			-20px 20px 30px -10px rgba(0, 56, 32, 0.2),
			0 0 20px rgba(0, 56, 32, 0.1),
			inset 0 0 20px rgba(0, 56, 32, 0.1);
	}

	.icon-container:hover::before {
		opacity: 1;
		transform: translateZ(10px);
	}

	.icon-container svg {
		transition: all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
		transform-style: preserve-3d;
		will-change: transform;
	}

	@keyframes iconFloat3D {
		0% { 
			transform: translateZ(0) rotateX(0) rotateY(0);
			filter: drop-shadow(0 2px 4px rgba(0, 56, 32, 0.1));
		}
		25% { 
			transform: translateZ(20px) rotateX(8deg) rotateY(-8deg);
			filter: drop-shadow(0 6px 12px rgba(0, 56, 32, 0.15));
		}
		50% { 
			transform: translateZ(30px) rotateX(-5deg) rotateY(5deg);
			filter: drop-shadow(0 8px 16px rgba(0, 56, 32, 0.2));
		}
		75% { 
			transform: translateZ(20px) rotateX(8deg) rotateY(8deg);
			filter: drop-shadow(0 6px 12px rgba(0, 56, 32, 0.15));
		}
		100% { 
			transform: translateZ(0) rotateX(0) rotateY(0);
			filter: drop-shadow(0 2px 4px rgba(0, 56, 32, 0.1));
		}
	}

	.premium-card:hover .icon-container {
		animation: iconFloat3D 3s ease-in-out infinite;
	}

	.premium-card:hover .icon-container svg {
		animation: iconPulse3D 2s ease-in-out infinite;
	}

	@keyframes iconPulse3D {
		0%, 100% {
			transform: scale(1) translateZ(0);
			filter: drop-shadow(0 2px 4px rgba(0, 56, 32, 0.1));
		}
		50% {
			transform: scale(1.15) translateZ(40px);
			filter: drop-shadow(0 12px 24px rgba(0, 56, 32, 0.3));
		}
	}

	/* Add new keyframe animation for icons */
	@keyframes iconPulse {
		0% { transform: scale(1) translateZ(0); filter: drop-shadow(0 2px 4px rgba(0, 56, 32, 0.1)); }
		50% { transform: scale(1.15) translateZ(20px); filter: drop-shadow(0 8px 16px rgba(0, 56, 32, 0.2)); }
		100% { transform: scale(1) translateZ(0); filter: drop-shadow(0 2px 4px rgba(0, 56, 32, 0.1)); }
	}

	.premium-card:hover .icon-container {
		animation: iconFloat 3s ease-in-out infinite;
	}

	@keyframes iconFloat {
		0%, 100% { transform: translateZ(0); }
		50% { transform: translateZ(20px); }
	}

	.premium-card:hover .icon-container svg {
		animation: iconPulse 1.5s cubic-bezier(0.4, 0, 0.2, 1) infinite;
	}

	/* Premium hover effect */
	.premium-hover {
		transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
	}

	.premium-hover:hover {
		transform: translateY(-5px);
		box-shadow:
			0 20px 40px -10px rgba(0, 56, 32, 0.2),
			0 0 0 1px rgba(0, 56, 32, 0.1);
	}

	/* Enhanced gradient animation */
	.animate-gradient {
		background-size: 300% 300%;
		animation: gradientPosition 8s ease infinite;
	}

	@keyframes gradientPosition {
		0% { background-position: 0% 50%; }
		50% { background-position: 100% 50%; }
		100% { background-position: 0% 50%; }
	}
	/* Transforms */
	.transform-3d {
		transform-style: preserve-3d;
		perspective: 1000px;
	}

	/* Scale on Hover */
	.hover-scale-105 {
		transition: transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out;
	}

	.hover-scale-105:hover {
		transform: scale(1.05);
		box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
	}
</style>