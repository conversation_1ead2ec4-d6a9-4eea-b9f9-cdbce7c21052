---
import Layout from '../layouts/Layout.astro';
import '@styles/animations.css';
import {
    sendVerificationEmail,
    getCurrentUserEmail,
} from "@lib/auth";



let email = await getCurrentUserEmail(Astro.locals.pb);
let message = "";

if (Astro.request.method === "POST") {
    await sendVerificationEmail(Astro.locals.pb, email);
    message = "✅ Verification email sent";
}
---

<Layout title="Verify Email - 3Pay Global">
	<div class="relative max-w-[90rem] w-full mx-auto px-4 md:px-6 py-16 lg:py-24">
		<!-- Background Image (subtle texture) -->
		<div class="absolute inset-0 z-0 bg-[url('/images/subtle-green-texture.png')] opacity-5"></div>

		<div class="container-premium relative bg-white/80 backdrop-blur-xl border border-[#003820]/10 rounded-[2rem] overflow-hidden max-w-2xl mx-auto shadow-premium transform transition-all duration-500 hover:shadow-premium-hover">
			<div class="px-8 py-12 sm:px-12 text-center">
				<div class="mb-8">
					<svg class="w-16 h-16 mx-auto text-[#003820]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 19v-8.93a2 2 0 01.89-1.664l7-4.666a2 2 0 012.22 0l7 4.666A2 2 0 0121 10.07V19M3 19a2 2 0 002 2h14a2 2 0 002-2M3 19l6.75-4.5M21 19l-6.75-4.5M3 10l6.75 4.5M21 10l-6.75 4.5m0 0l-1.14.76a2 2 0 01-2.22 0l-1.14-.76"/>
					</svg>
				</div>
				
				<h1 class="text-3xl md:text-4xl font-bold mb-4 text-[#003820]">
					Check Your Email
				</h1>
				
				<p class="text-lg text-[#3C4145]/80 mb-8">
					We've sent you a verification email. Please check your inbox and click the verification link to complete your registration.
				</p>

				<div class="bg-[#003820]/5 rounded-lg p-6 mb-8">
					<h2 class="font-semibold text-[#003820] mb-3">Haven't received the email?</h2>
					<ul class="text-[#3C4145]/80 text-left space-y-2">
						<li class="flex items-start">
							<svg class="w-5 h-5 mr-2 mt-0.5 text-[#003820]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
							</svg>
							Check your spam or junk folder
						</li>
						<li class="flex items-start">
							<svg class="w-5 h-5 mr-2 mt-0.5 text-[#003820]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
							</svg>
							Make sure you entered the correct email address
						</li>
						<li class="flex items-start">
							<svg class="w-5 h-5 mr-2 mt-0.5 text-[#003820]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
							</svg>
							Allow a few minutes for the email to arrive
						</li>
					</ul>
				</div>

				{message && <p class="text-green-500">{message}</p>}

				<form method="POST">
					<button type="submit" class="bg-[#003820] text-white py-2 px-4 rounded-md hover:bg-[#005732] transition-colors">
						Resend Verification Email
					</button>
				</form>
				

				<a href="/" class="inline-flex items-center text-[#003820] hover:text-[#005732] transition-colors mt-4">
					<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
					</svg>
					Return to Home
				</a>
			</div>
		</div>
	</div>
</Layout>

<style>
	.container-premium {
		background: rgba(255, 255, 255, 0.9);
		border: 1px solid rgba(0, 56, 32, 0.08);
		backdrop-filter: blur(24px);
		transition: all 0.4s ease-in-out;
		box-shadow: 0 0.75rem 2.25rem rgba(0, 56, 32, 0.05);
	}

	.shadow-premium {
		box-shadow: 0 4px 6px -1px rgba(0, 56, 32, 0.1), 0 2px 4px -1px rgba(0, 56, 32, 0.06);
	}

	.shadow-premium-hover {
		box-shadow: 0 20px 25px -5px rgba(0, 56, 32, 0.1), 0 10px 10px -5px rgba(0, 56, 32, 0.04);
	}
</style>