---
import Layout from '@layouts/Layout.astro'
import { getCollection } from 'astro:content'
import type { CollectionEntry } from 'astro:content'
import '../styles/animations.css'


const posts = await getCollection('blog')
const sortedPosts = posts.sort(
  (a: CollectionEntry<'blog'>, b: CollectionEntry<'blog'>) => 
    new Date(b.data.datePublished).valueOf() - new Date(a.data.datePublished).valueOf()
)

const formattedDate = (date: string) =>
  new Date(date).toLocaleDateString('en-us', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  })
---

<Layout title="Litigation Funding Blog | 3Pay Global">
  <section class="w-full">
    <div class="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 sm:py-12 md:py-16 lg:py-20">
      <div class="flex flex-col gap-8 md:gap-12 lg:gap-16">
        <div class="flex flex-col items-center text-center space-y-6 fade-in mt-10">
          <div class="inline-flex items-center bg-[#003820]/5 shadow-[rgba(0,0,0,0.08)_0_0_1px_1px,rgba(255,255,255,0.25)_4px_4px_4px_inset] text-[#003820] px-4 py-2 rounded-full">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" class="w-4 h-4 mr-2">
              <path d="M2.5 2.5C2.5 1.67157 3.17157 1 4 1H12C12.8284 1 13.5 1.67157 13.5 2.5V13.5C13.5 14.3284 12.8284 15 12 15H4C3.17157 15 2.5 14.3284 2.5 13.5V2.5Z" stroke="currentColor" stroke-width="1.3"/>
              <path d="M5 4.5H11" stroke="currentColor" stroke-width="1.3" stroke-linecap="round"/>
              <path d="M5 7.5H11" stroke="currentColor" stroke-width="1.3" stroke-linecap="round"/>
              <path d="M5 10.5H9" stroke="currentColor" stroke-width="1.3" stroke-linecap="round"/>
            </svg>
            <span class="text-sm">Latest Updates</span>
                    </div>
                    <h2 class="text-3xl md:text-5xl lg:text-6xl font-normal tracking-tight">
                      Expert Insights on<br/><span class="text-[#003820]">Litigation Funding</span>
                    </h2>
                    <p class="text-[#3C4145] text-base max-w-2xl">
                        Stay informed about the latest developments in litigation funding, legal strategies, and successful case outcomes.
                    </p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {sortedPosts.map((post) => (
                        <div class="group slide-in-right">
                            <a href={`/blog/${post.slug}/`} class="flex flex-col h-full overflow-hidden bg-white rounded-3xl border border-[#003820]/10 hover:border-[#003820]/30 transition-all duration-300 hover:-translate-y-1 hover:shadow-lg">
                                <div class="relative aspect-[16/12] overflow-hidden">
                                    <img src={`${post.data.metaImage}`} alt={post.data.title} class="w-full h-full object-cover" />
                                </div>
                                <div class="flex flex-col flex-grow p-6 space-y-4">
                                    <div class="flex flex-wrap gap-2">
                                        <div class="inline-flex items-center bg-[#003820]/5 px-3 py-1 rounded-full">
                                            <span class="text-xs text-[#003820]">{formattedDate(post.data.datePublished)}</span>
                                        </div>
                                    </div>
                                    <h3 class="text-xl font-medium text-gray-900 group-hover:text-[#003820] transition-colors">
                                        {post.data.title}
                                    </h3>
                                    <p class="text-[#3C4145] text-sm flex-grow">
                                        {post.data.summary}
                                    </p>
                                    <div class="inline-flex items-center text-[#003820] font-medium">
                                        Read More
                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4 ml-1">
                                            <path fill-rule="evenodd" d="M3 10a.75.75 0 01.75-.75h10.638L10.23 5.29a.75.75 0 111.04-1.08l5.5 5.25a.75.75 0 010 1.08l-5.5 5.25a.75.75 0 11-1.04-1.08l4.158-3.96H3.75A.75.75 0 013 10z" clip-rule="evenodd" />
                                        </svg>
                                    </div>
                                </div>
                            </a>
                        </div>
                    ))}
                </div>
            </div>
        </div>
    </section>
</Layout>

