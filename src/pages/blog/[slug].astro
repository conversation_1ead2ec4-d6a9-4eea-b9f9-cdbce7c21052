---
export const prerender = true
import MarkdownLayout from '@layouts/MarkdownLayout.astro'
import { getCollection } from 'astro:content'
import type { CollectionEntry } from 'astro:content'

export async function getStaticPaths() {
  const posts = await getCollection('blog')
  return posts.map((post: CollectionEntry<'blog'>) => ({
    params: { slug: post.slug },
    props: { post },
  }))
}

const { post } = Astro.props
const { Content } = await post.render()
---

<MarkdownLayout 
  title={post.data.title} 
  date={post.data.datePublished} 
  image={post.data.metaImage} 
  previousPost={post.data.previousPost}
  previousPostTitle={post.data.previousPostTitle}
  nextPostTitle={post.data.nextPostTitle}
  nextPost={post.data.nextPost}
>
  <Content />
</MarkdownLayout>