---
import Layout from '@layouts/Layout.astro';

---

<Layout title="Download Our App!">
	<div class="min-h-screen flex items-center justify-center bg-gradient-to-br from-indigo-200 via-green-200 to-pink-200">
		<div class="bg-white rounded-2xl shadow-xl p-8 md:p-12 max-w-2xl mx-auto text-center">
			<h1 class="text-4xl md:text-5xl font-bold text-gray-800 mb-6 animate-bounce">
				Get More Done On the Go!
			</h1>

			<p class="text-lg text-gray-700 mb-8">
				Download our amazing mobile app and experience all the features you love, right in your pocket!  Stay connected, organized, and productive wherever you are.
			</p>

			<div class="flex flex-col md:flex-row items-center justify-center space-y-4 md:space-y-0 md:space-x-6">
				<a href="[YOUR ANDROID APP LINK]" class="app-button android-button animate-float">
					<img src="/images/google-play-badge.png" alt="Get it on Google Play" class="w-40 hover:scale-105 transition-transform" loading="lazy">
				</a>

				<a href="[YOUR APP STORE LINK]" class="app-button ios-button animate-float-reverse">
					<img src="/images/app-store-badge.png" alt="Download on the App Store" class="w-40 hover:scale-105 transition-transform" loading="lazy">
				</a>
			</div>

			<div class="mt-8">
				<p class="text-sm text-gray-500">
					Available on both Android and iOS.  Enjoy!
				</p>
			</div>
		</div>
	</div>
</Layout>

<style>
	.app-button {
		@apply rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300;
	}

	.android-button {
		background-color: #3DDC84; /* Google Play Green */
	}

	.ios-button {
		background-color: #000000; /* App Store Black */
	}

	.animate-bounce {
		animation: bounce 2s infinite;
	}

	@keyframes bounce {
		0%, 20%, 50%, 80%, 100% {
			transform: translateY(0);
		}
		40% {
			transform: translateY(-10px);
		}
		60% {
			transform: translateY(-5px);
		}
	}

	.animate-float {
		animation: float 6s ease-in-out infinite;
	}

	.animate-float-reverse {
		animation: float 7s ease-in-out infinite reverse;
	}

	@keyframes float {
		0% { transform: translateY(0); }
		50% { transform: translateY(-8px); }
		100% { transform: translateY(0); }
	}
</style>