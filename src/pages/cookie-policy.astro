---
import Layout from '../layouts/Layout.astro';
import '@styles/animations.css';
---

<Layout title="Cookie Policy - 3Pay Global">
	<div class="relative w-full overflow-hidden">
		<!-- Hero Section -->
		<section class="relative min-h-screen flex items-center justify-center py-20 bg-[#003820]/5">
			<div class="absolute inset-0 overflow-hidden pointer-events-none">
				<div class="absolute top-0 left-1/4 w-[45rem] h-[45rem] bg-gradient-to-br from-[#003820]/10 to-[#003820]/5 rounded-full blur-[160px] mix-blend-soft-light animate-float-slow"></div>
				<div class="absolute bottom-0 right-1/4 w-[35rem] h-[35rem] bg-gradient-to-tr from-[#003820]/15 to-[#003820]/5 rounded-full blur-[128px] mix-blend-soft-light animate-float-reverse"></div>
				<div class="absolute inset-0 bg-grid opacity-[0.015] mix-blend-overlay"></div>
			</div>

			<div class="container mx-auto px-4 relative z-10">
				<div class="max-w-4xl mx-auto">
					<h1 class="text-5xl md:text-7xl font-bold mb-10 mt-12 text-center animate-gradient bg-[length:200%_auto] bg-clip-text text-transparent bg-gradient-to-r from-[#003820] via-[#005732] to-[#003820]">
						Cookie Policy
					</h1>
			
					<div class="bg-white p-8 md:p-10 rounded-2xl shadow-xl">
						<p class="mb-6 text-lg leading-relaxed">
							Our website uses cookies to distinguish you from other users of our website. This helps us to provide you with a good experience when you browse our website and also allows us to improve our site.
						</p>
						
						<p class="mb-8 text-lg leading-relaxed">
							A cookie is a small file of letters and numbers that we store on your browser or the hard drive of your computer if you agree. Cookies contain information that is transferred to your computer's hard drive.
						</p>
						
						<div class="mb-8 pb-8 border-b border-gray-100">
							<p class="font-medium text-xl mb-6 text-[#003820]">We use the following cookies:</p>
							
							<div class="space-y-6">
								<div class="cookie-type-card">
									<h3 class="cookie-type-title">Strictly necessary cookies</h3>
									<p class="cookie-type-description">These are cookies that are required for the operation of our website. They include, for example, cookies that enable you to log into secure areas of our website, use a shopping cart or make use of e-billing services.</p>
								</div>
								
								<div class="cookie-type-card">
									<h3 class="cookie-type-title">Analytical or performance cookies</h3>
									<p class="cookie-type-description">These allow us to recognise and count the number of visitors and to see how visitors move around our website when they are using it. This helps us to improve the way our website works, for example, by ensuring that users are finding what they are looking for easily.</p>
								</div>
								
								<div class="cookie-type-card">
									<h3 class="cookie-type-title">Functionality cookies</h3>
									<p class="cookie-type-description">These are used to recognise you when you return to our website. This enables us to personalise our content for you, greet you by name and remember your preferences (for example, your choice of language or region).</p>
								</div>
								
								<div class="cookie-type-card">
									<h3 class="cookie-type-title">Targeting cookies</h3>
									<p class="cookie-type-description">These cookies record your visit to our website, the pages you have visited and the links you have followed. We will use this information to make our website and the advertising displayed on it more relevant to your interests. We may also share this information with third parties for this purpose.</p>
								</div>
							</div>
						</div>
						
						<p class="mb-8 text-lg leading-relaxed">
							You can find more information about the individual cookies we use and the purposes for which we use them below:
						</p>
						
						<div class="mb-8 pb-8 border-b border-gray-100">
							<h2 class="text-2xl font-bold mb-4 text-[#003820]">What are cookies?</h2>
							
							<p class="mb-4 leading-relaxed">
								Cookies are small files that websites send to your device that the sites then uses to monitor you and remember certain information about you, for example your login information.
							</p>
							
							<p class="mb-4 leading-relaxed">
								Session cookies disappear once the user closes the browser window. However 3Pay Global cookies last up to 12 months.
							</p>
						</div>
						
						<div class="mb-8 pb-8 border-b border-gray-100">
							<h2 class="text-2xl font-bold mb-4 text-[#003820]">3Pay Global Cookie</h2>
							
							<p class="mb-6 leading-relaxed">
								This cookie is essential for our site to OR enables us to:
							</p>
							
							<ul class="space-y-3 mb-6 pl-6">
								<li class="flex items-start">
									<span class="inline-flex items-center justify-center h-6 w-6 rounded-full bg-[#003820]/10 text-[#003820] font-medium text-sm mr-3 mt-0.5">a</span>
									<span class="leading-relaxed">Estimate our audience size and usage pattern.</span>
								</li>
								<li class="flex items-start">
									<span class="inline-flex items-center justify-center h-6 w-6 rounded-full bg-[#003820]/10 text-[#003820] font-medium text-sm mr-3 mt-0.5">b</span>
									<span class="leading-relaxed">Store information about your preferences, and so allow us to customise our site and to provide you with offers that are targeted to your individual interests.</span>
								</li>
								<li class="flex items-start">
									<span class="inline-flex items-center justify-center h-6 w-6 rounded-full bg-[#003820]/10 text-[#003820] font-medium text-sm mr-3 mt-0.5">c</span>
									<span class="leading-relaxed">Speed up your searches.</span>
								</li>
								<li class="flex items-start">
									<span class="inline-flex items-center justify-center h-6 w-6 rounded-full bg-[#003820]/10 text-[#003820] font-medium text-sm mr-3 mt-0.5">d</span>
									<span class="leading-relaxed">Recognise you when you return to our site.</span>
								</li>
								<li class="flex items-start">
									<span class="inline-flex items-center justify-center h-6 w-6 rounded-full bg-[#003820]/10 text-[#003820] font-medium text-sm mr-3 mt-0.5">e</span>
									<span class="leading-relaxed">Allow you to use our site in a way that makes your browsing experience more convenient, for example, by allowing you to store items in an electronic shopping basket between visits. If you register with us or complete our online forms, we will use cookies to remember your details during your current visit, and any future visits provided the cookie was not deleted in the interim.</span>
								</li>
							</ul>
						</div>
						
						<div class="mb-8">
							<p class="mb-6 leading-relaxed">
								Please note that the following third parties may also use cookies, over which we have no control. These named third parties may include, for example, advertising networks and providers of external services like web traffic analysis services. These third-party cookies are likely to be analytical cookies or performance cookies or targeting cookies:
							</p>
							
							<ul class="space-y-3 mb-8 pl-6">
								<li class="flex items-start">
									<span class="inline-flex items-center justify-center h-6 w-6 rounded-full bg-[#003820]/10 text-[#003820] font-medium text-sm mr-3 mt-0.5">1</span>
									<span class="leading-relaxed">Insolvency & Law Limited;</span>
								</li>
								<li class="flex items-start">
									<span class="inline-flex items-center justify-center h-6 w-6 rounded-full bg-[#003820]/10 text-[#003820] font-medium text-sm mr-3 mt-0.5">2</span>
									<span class="leading-relaxed">Intelicred Limited;</span>
								</li>
								<li class="flex items-start">
									<span class="inline-flex items-center justify-center h-6 w-6 rounded-full bg-[#003820]/10 text-[#003820] font-medium text-sm mr-3 mt-0.5">3</span>
									<span class="leading-relaxed">Money Matters Limited;</span>
								</li>
							</ul>
							
							<div class="bg-[#003820]/5 rounded-xl p-6 mb-8">
								<p class="mb-2 leading-relaxed">
									To deactivate the use of third party advertising cookies, you may visit the consumer page to manage the use of these types of cookies.
								</p>
								<a href="#cookie-settings" class="inline-flex items-center text-[#003820] font-medium hover:text-[#005732] transition-colors">
									Click here for cookie settings
									<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-1" viewBox="0 0 20 20" fill="currentColor">
										<path fill-rule="evenodd" d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd" />
									</svg>
								</a>
							</div>
							
							<p class="mb-6 leading-relaxed">
								You can block cookies by activating the setting on your browser that allows you to refuse the setting of all or some cookies. However, if you use your browser settings to block all cookies (including essential cookies) you may not be able to access all or parts of our website.
							</p>
							
							<p class="leading-relaxed">
								Except for essential cookies, all cookies will expire after 12 months.
							</p>
						</div>
					</div>
				</div>
			</div>
		</section>
			
		<!-- Cookie Settings Modal (hidden by default) -->
		<div id="cookie-settings-modal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
			<div class="bg-white rounded-xl p-8 max-w-2xl w-full max-h-[90vh] overflow-y-auto">
				<div class="flex justify-between items-center mb-6">
					<h2 class="text-2xl font-bold text-[#003820]">Cookie Settings</h2>
					<button id="close-cookie-modal" class="text-gray-500 hover:text-gray-700 transition-colors">
						<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
						</svg>
					</button>
				</div>
				
				<div class="space-y-6">
					<div class="p-4 rounded-lg bg-gray-50">
						<div class="flex items-center justify-between mb-2">
							<h3 class="font-medium text-[#003820]">Strictly Necessary Cookies</h3>
							<div class="relative inline-block w-12 h-6 bg-gray-200 rounded-full cursor-not-allowed">
								<div class="absolute left-1 top-1 bg-[#003820] w-4 h-4 rounded-full transition-transform transform translate-x-6"></div>
							</div>
						</div>
						<p class="text-sm text-gray-600">These cookies are necessary for the website to function and cannot be switched off.</p>
					</div>
					
					<div class="p-4 rounded-lg bg-gray-50">
						<div class="flex items-center justify-between mb-2">
							<h3 class="font-medium text-[#003820]">Analytical Cookies</h3>
							<label class="relative inline-block w-12 h-6 bg-gray-200 rounded-full cursor-pointer">
								<input type="checkbox" class="sr-only peer" checked>
								<div class="absolute left-1 top-1 bg-gray-400 w-4 h-4 rounded-full transition-transform peer-checked:bg-[#003820] peer-checked:transform peer-checked:translate-x-6"></div>
							</label>
						</div>
						<p class="text-sm text-gray-600">These cookies allow us to count visits and traffic sources so we can measure and improve the performance of our site.</p>
					</div>
					
					<div class="p-4 rounded-lg bg-gray-50">
						<div class="flex items-center justify-between mb-2">
							<h3 class="font-medium text-[#003820]">Functionality Cookies</h3>
							<label class="relative inline-block w-12 h-6 bg-gray-200 rounded-full cursor-pointer">
								<input type="checkbox" class="sr-only peer" checked>
								<div class="absolute left-1 top-1 bg-gray-400 w-4 h-4 rounded-full transition-transform peer-checked:bg-[#003820] peer-checked:transform peer-checked:translate-x-6"></div>
							</label>
						</div>
						<p class="text-sm text-gray-600">These cookies enable personalized features and functionality.</p>
					</div>
					
					<div class="p-4 rounded-lg bg-gray-50">
						<div class="flex items-center justify-between mb-2">
							<h3 class="font-medium text-[#003820]">Targeting Cookies</h3>
							<label class="relative inline-block w-12 h-6 bg-gray-200 rounded-full cursor-pointer">
								<input type="checkbox" class="sr-only peer" checked>
								<div class="absolute left-1 top-1 bg-gray-400 w-4 h-4 rounded-full transition-transform peer-checked:bg-[#003820] peer-checked:transform peer-checked:translate-x-6"></div>
							</label>
						</div>
						<p class="text-sm text-gray-600">These cookies may be set by our advertising partners to build a profile of your interests.</p>
					</div>
				</div>
				
				<div class="mt-8 flex justify-end">
					<button id="save-cookie-preferences" class="px-6 py-3 bg-[#003820] text-white rounded-lg hover:bg-[#004d2d] transition-colors">
						Save Preferences
					</button>
				</div>
			</div>
		</div>
	</div>
</Layout>

<script>
	document.addEventListener('DOMContentLoaded', function() {
		const cookieSettingsLink = document.querySelector('a[href="#cookie-settings"]');
		const cookieModal = document.getElementById('cookie-settings-modal');
		const closeModal = document.getElementById('close-cookie-modal');
		const savePreferences = document.getElementById('save-cookie-preferences');
		
		if (cookieSettingsLink && cookieModal) {
			// Open modal when clicking the settings link
			cookieSettingsLink.addEventListener('click', function(e) {
				e.preventDefault();
				cookieModal.classList.remove('hidden');
				document.body.classList.add('overflow-hidden');
			});
		}
		
		if (closeModal) {
			// Close modal when clicking the close button
			closeModal.addEventListener('click', function() {
				cookieModal.classList.add('hidden');
				document.body.classList.remove('overflow-hidden');
			});
		}
		
		if (savePreferences) {
			// Save preferences and close modal
			savePreferences.addEventListener('click', function() {
				// Here you would save the user's preferences
				// For demonstration, we'll just close the modal
				cookieModal.classList.add('hidden');
				document.body.classList.remove('overflow-hidden');
				
				// Show confirmation message
				const confirmationMessage = document.createElement('div');
				confirmationMessage.className = 'fixed bottom-4 right-4 bg-[#e6f4ee] border border-[#003820] text-[#003820] px-4 py-3 rounded-lg shadow-lg';
				confirmationMessage.innerHTML = `
					<div class="flex items-center">
						<svg class="h-5 w-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
							<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
						</svg>
						<p>Your cookie preferences have been saved.</p>
					</div>
				`;
				document.body.appendChild(confirmationMessage);
				
				// Remove confirmation after 3 seconds
				setTimeout(() => {
					confirmationMessage.remove();
				}, 3000);
			});
		}
		
		// Close the modal when clicking outside of it
		window.addEventListener('click', function(e) {
			if (e.target === cookieModal) {
				cookieModal.classList.add('hidden');
				document.body.classList.remove('overflow-hidden');
			}
		});
	});
</script>

<style>
	/* Background grid pattern */
	.bg-grid {
		background-size: 40px 40px;
		background-image: 
			linear-gradient(to right, rgba(0, 56, 32, 0.05) 1px, transparent 1px),
			linear-gradient(to bottom, rgba(0, 56, 32, 0.05) 1px, transparent 1px);
	}
	
	/* Animation utilities */
	.animate-float-slow {
		animation: float 8s ease-in-out infinite;
	}
	
	.animate-float-reverse {
		animation: float 9s ease-in-out infinite reverse;
	}
	
	@keyframes float {
		0%, 100% {
			transform: translateY(0);
		}
		50% {
			transform: translateY(-20px);
		}
	}
	
	.animate-gradient {
		animation: gradientShift 8s ease infinite;
	}
	
	@keyframes gradientShift {
		0%, 100% {
			background-position: 0% 50%;
		}
		50% {
			background-position: 100% 50%;
		}
	}
	
	/* Custom styles for cookie types */
	.cookie-type-card {
		position: relative;
		padding: 1.5rem;
		padding-left: 2rem;
		background-color: rgba(0, 56, 32, 0.03);
		border-radius: 0.75rem;
		border-left: 4px solid #003820;
	}
	
	.cookie-type-title {
		font-weight: 600;
		font-size: 1.125rem;
		margin-bottom: 0.5rem;
		color: #003820;
	}
	
	.cookie-type-description {
		line-height: 1.6;
	}
	
	/* Toggle switch styling */
	.peer-checked\:bg-\[\#003820\]:checked + div {
		background-color: #003820;
	}
	
	.peer-checked\:transform:checked + div {
		transform: translateX(1.5rem);
	}
</style>