---
// Contact Page for 3Pay Global
import Layout from '../layouts/Layout.astro';
---

<Layout title="Contact 3Pay Global - Litigation Funding">
    <div class="relative w-full overflow-hidden">
		<!-- Hero Section -->
		<section class="relative min-h-screen flex items-center justify-center py-20 bg-[#003820]/5">
			<div class="absolute inset-0 overflow-hidden pointer-events-none">
				<div class="absolute top-0 left-1/4 w-[45rem] h-[45rem] bg-gradient-to-br from-[#003820]/10 to-[#003820]/5 rounded-full blur-[160px] mix-blend-soft-light animate-float-slow"></div>
				<div class="absolute bottom-0 right-1/4 w-[35rem] h-[35rem] bg-gradient-to-tr from-[#003820]/15 to-[#003820]/5 rounded-full blur-[128px] mix-blend-soft-light animate-float-reverse"></div>
				<div class="absolute inset-0 bg-grid opacity-[0.015] mix-blend-overlay"></div>
			</div>
            
        <div class="container mx-auto px-4 relative z-10">
            <div class="max-w-4xl mx-auto text-center">
                <h1 class="text-5xl md:text-7xl font-bold mb-6 mt-12 animate-gradient bg-[length:200%_auto] bg-clip-text text-transparent bg-gradient-to-r from-[#003820] via-[#005732] to-[#003820]">
                    Contact Us
                </h1>
            </div>
      
      <div class="grid md:grid-cols-2 gap-8 max-w-5xl mx-auto">
        <!-- Contact Information Card -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
          <div class="p-6 border-b">
            <h2 class="text-2xl font-semibold">Our London Office</h2>
          </div>
          <div class="p-6 space-y-6">
            <div class="flex items-start">
              <span class="text-emerald-700 mr-3 mt-1 flex-shrink-0">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide-map-pin"><path d="M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z"></path><circle cx="12" cy="10" r="3"></circle></svg>
              </span>
              <p>78 York Street, London, W1H 1DP</p>
            </div>
            
            <div class="flex items-start">
              <span class="text-emerald-700 mr-3 mt-1 flex-shrink-0">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide-phone"><path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path></svg>
              </span>
              <p>+44 (2)076 9289 77</p>
            </div>
            
            <div class="flex items-start">
              <span class="text-emerald-700 mr-3 mt-1 flex-shrink-0">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide-mail"><rect width="20" height="16" x="2" y="4" rx="2"></rect><path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"></path></svg>
              </span>
              <p><EMAIL></p>
            </div>
            
            <div class="border-t my-4 pt-4"></div>
            
            <div>
              <h3 class="font-medium mb-3">Connect With Us</h3>
              <div class="flex space-x-4">
                <a href="#" class="p-2 rounded-full bg-slate-100 hover:bg-emerald-100 transition-colors">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-emerald-700">
                    <path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"></path>
                  </svg>
                </a>
                <a href="#" class="p-2 rounded-full bg-slate-100 hover:bg-emerald-100 transition-colors">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-emerald-700">
                    <path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"></path>
                    <rect x="2" y="9" width="4" height="12"></rect>
                    <circle cx="4" cy="4" r="2"></circle>
                  </svg>
                </a>
                <a href="#" class="p-2 rounded-full bg-slate-100 hover:bg-emerald-100 transition-colors">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-emerald-700">
                    <circle cx="12" cy="12" r="10"></circle>
                    <circle cx="12" cy="12" r="4"></circle>
                    <path d="M4.93 4.93l4.24 4.24"></path>
                    <path d="M14.83 14.83l4.24 4.24"></path>
                    <path d="M14.83 9.17l4.24-4.24"></path>
                    <path d="M9.17 14.83l-4.24 4.24"></path>
                  </svg>
                </a>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Map Card -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
          <div class="h-full min-h-[400px]">
            <iframe 
              src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2482.7662603779584!2d-0.15731492342157214!3d51.51801677181147!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x48761b0289578d47%3A0x4552cd7313cc5c0!2s78%20York%20St%2C%20London%20W1H%201DP%2C%20UK!5e0!3m2!1sen!2sus!4v1647851824288!5m2!1sen!2sus" 
              width="100%" 
              height="100%" 
              style="border: 0; min-height: 400px;" 
              allowfullscreen
              loading="lazy"
              title="3Pay Global office location">
            </iframe>
          </div>
        </div>
      </div>
      
      <!-- Contact Form Section
      <div class="mt-12 bg-white rounded-lg shadow-md max-w-5xl mx-auto">
        <div class="p-6 border-b">
          <h2 class="text-2xl font-semibold">Send Us a Message</h2>
        </div>
        <div class="p-6">
          <form id="contactForm" class="space-y-6">
            <div class="grid md:grid-cols-2 gap-6">
              <div class="space-y-2">
                <label for="name" class="block font-medium">
                  Full Name
                </label>
                <input 
                  type="text" 
                  id="name" 
                  name="name" 
                  class="w-full px-4 py-2 border border-slate-200 rounded-md focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                  required
                />
              </div>
              
              <div class="space-y-2">
                <label for="email" class="block font-medium">
                  Email Address
                </label>
                <input 
                  type="email" 
                  id="email" 
                  name="email" 
                  class="w-full px-4 py-2 border border-slate-200 rounded-md focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                  required
                />
              </div>
            </div>
            
            <div class="space-y-2">
              <label for="subject" class="block font-medium">
                Subject
              </label>
              <input 
                type="text" 
                id="subject" 
                name="subject" 
                class="w-full px-4 py-2 border border-slate-200 rounded-md focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                required
              />
            </div>
            
            <div class="space-y-2">
              <label for="message" class="block font-medium">
                Message
              </label>
              <textarea 
                id="message" 
                name="message" 
                rows="5"
                class="w-full px-4 py-2 border border-slate-200 rounded-md focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                required
              ></textarea>
            </div>
            
            <div>
              <button 
                type="submit" 
                class="px-6 py-3 bg-emerald-700 text-white font-medium rounded-md hover:bg-emerald-800 transition-colors"
              >
                Send Message
              </button>
            </div>
          </form> -->
        </div>
      </div>
    </main>
  </div>
  </div>
  </section>

</Layout>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('contactForm');
    
    if (form) {
      form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Get form values
        const name = document.getElementById('name').value;
        const email = document.getElementById('email').value;
        const subject = document.getElementById('subject').value;
        const message = document.getElementById('message').value;
        
        // Here you would typically send the data to your server
        // For now, we'll just log it to console
        console.log({
          name,
          email,
          subject,
          message
        });
        
        // Show success message (in a real application)
        alert('Thank you for your message. We will be in touch soon!');
        
        // Reset form
        form.reset();
      });
    }
  });
</script>

<style>
  /* Custom styles */
  @keyframes pulse {
    0%, 100% {
      opacity: 0.5;
    }
    50% {
      opacity: 0.8;
    }
  }
  
  .animate-pulse {
    animation: pulse 8s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }
  
  .bg-grid {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100'%3E%3Cpath d='M0 0h100v1H0zM0 0h1v100H0z' fill='%23000' fill-opacity='0.2'/%3E%3C/svg%3E");
    background-position: center;
  }
  
  /* Tailwind-like utility classes for emerald colors */
  .text-emerald-700 {
    color: #047857;
  }
  
  .bg-emerald-700 {
    background-color: #047857;
  }
  
  .bg-emerald-800 {
    background-color: #065f46;
  }
  
  .bg-emerald-100 {
    background-color: #d1fae5;
  }
  
  .from-emerald-900\/10 {
    --tw-gradient-from: rgb(6 78 59 / 0.1);
  }
  
  .to-emerald-900\/5 {
    --tw-gradient-to: rgb(6 78 59 / 0.05);
  }
  
  .from-emerald-900\/15 {
    --tw-gradient-from: rgb(6 78 59 / 0.15);
  }
  
  .focus\:ring-emerald-500:focus {
    --tw-ring-color: #10b981;
    box-shadow: 0 0 0 2px var(--tw-ring-color);
  }
  
  .hover\:bg-emerald-800:hover {
    background-color: #065f46;
  }
  
  .hover\:bg-emerald-100:hover {
    background-color: #d1fae5;
  }
</style>