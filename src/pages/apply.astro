---
import Layout from '../layouts/Layout.astro';
import '@styles/animations.css';
import { createUser, sendVerificationEmail, createSolicitor, createCoFunder, loginUser, getCurrentUserId, setCookieAndRedirectToDashboard } from '@lib/auth';
import { UsersUserTypeOptions } from '@data/pocketbase-types';

let error = '';

if (Astro.request.method === 'POST') {
  try {
    const formData = await Astro.request.formData();
    const data = Object.fromEntries(formData);
    
    // Validate user type
    const validUserTypes = Object.values(UsersUserTypeOptions);
    if (!validUserTypes.includes(data.user_type as UsersUserTypeOptions)) {
      throw new Error('Invalid user type selected');
    }

    // Create user in Pocketbase with address fields if present
    const user = await createUser(
      Astro.locals.pb,
      data.email.toString(),
      data.password.toString(),
      data.user_type as UsersUserTypeOptions,
      data.fname.toString(),
      data.lname.toString(),
      data.mobile.toString(),
      data.address_line1?.toString(),
      data.address_line2?.toString(),
      data.city?.toString(),
      data.state_province?.toString(), 
      data.postcode?.toString(),
      data.law_firm_name?.toString() || '',
      data.position?.toString() || '',
    );
    await loginUser(Astro.locals.pb, data.email.toString() as string, data.password.toString() as string);
		await sendVerificationEmail(Astro.locals.pb, data.email.toString() as string);
		// const user_id = await getCurrentUserId(Astro.locals.pb);

    // Create user type specific records
    if (data.user_type === 'solicitor') {
  await createSolicitor(
    Astro.locals.pb,
    user.id,
    data.law_firm_name?.toString() || '',
    data.address_line1?.toString() || '',  // firm_address
    `${data.fname} ${data.lname}`,        // solicitor_name
    data.position?.toString() || '',       // solicitor_position
    data.mobile.toString(),                // contact_number
    data.sra_number?.toString() || ''

      );
    } else if (data.user_type === 'co-funder') {
      await createCoFunder(
      Astro.locals.pb,
      user.id
      );
    }


    // Send verification email
    await sendVerificationEmail(Astro.locals.pb, data.email.toString());

    // Redirect to verification page
    return setCookieAndRedirectToDashboard(Astro.locals.pb);
  } catch (err: any) {
    error = err.message || 'An error occurred during registration';
  }
}
---

<Layout title="Apply - 3Pay Global">
  <div class="relative max-w-[90rem] w-full mx-auto px-4 md:px-6 py-16 lg:py-24">
    <!-- Background Image (subtle texture) -->
    <div class="absolute inset-0 z-0 bg-[url('/images/subtle-green-texture.png')] opacity-5"></div>

    <div class="container-premium relative bg-white/80 backdrop-blur-xl border border-[#003820]/10 rounded-[2rem] overflow-hidden max-w-4xl mx-auto shadow-premium transform transition-all duration-500 hover:shadow-premium-hover">
      <div class="relative px-8 py-12 sm:px-10 sm:py-14 text-center border-b border-[#003820]/10">
        <h1 class="text-4xl md:text-5xl font-bold mb-4 text-[#003820]">
          Application
        </h1>
        <p class="text-lg text-[#3C4145]/80">
          Already have an account?
          <a href="/login" class="text-[#003820] hover:text-[#005732] font-medium ml-2 transition-colors">
            Login
          </a>
        </p>
      </div>

      <!-- Form Container -->
      <div class="relative px-6 py-8 sm:px-10 lg:px-16">
        <form class="space-y-6 md:space-y-8" method="post" id="applicationForm" onsubmit="return validateForm(event)">
          <!-- Error message -->
          {error && (
            <div class="p-4 bg-[#f8d7da] border-l-4 border-[#f07167] rounded-lg mb-6 text-[#842029]">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                  </svg>
                </div>
                <div class="ml-3">
                  <p class="text-sm font-medium">{error}</p>
                </div>
              </div>
            </div>
          )}

          <!-- Form fields -->
          <div class="space-y-5 md:space-y-7 mb-10">
            <!-- User Type Selection -->
            <div class="form-field">
              <label for="userType" class="form-label">Application Type<span class="text-[#003820]">*</span></label>
                <select name="user_type" id="userType" required class="form-input">
                <option value="" disabled selected>Select application type</option>
                <option value="co-funder">Co-funder</option>
                <option value="solicitor">Solicitor</option>
                <option value="claimant">Claimant</option>
                </select>
            </div>

            <!-- Personal Information -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-5">
              <div class="form-field">
                <label for="fname" class="form-label">First Name<span class="text-[#003820]">*</span></label>
                <input type="text" name="fname" id="fname" required minlength="2" class="form-input" placeholder="Enter your first name" />
              </div>
              <div class="form-field">
                <label for="lname" class="form-label">Last Name<span class="text-[#003820]">*</span></label>
                <input type="text" name="lname" id="lname" required minlength="2" class="form-input" placeholder="Enter your last name" />
              </div>
            </div>

            <div class="form-field">
              <label for="email" class="form-label">Email<span class="text-red-500">*</span></label>
              <input type="email" name="email" id="email" required class="form-input" placeholder="Enter your email" />
            </div>
  
            <!-- Phone Number -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="form-field">
              <label for="country-code" class="form-label">Country Code<span class="text-[#003820]">*</span></label>
              <select name="country_code" id="country-code" required class="form-input">
                <option value="">Select country</option>
              </select>
              </div>
              <div class="form-field">
              <label for="mobile" class="form-label">Mobile number<span class="text-[#003820]">*</span></label>
              <input type="tel" name="mobile" id="mobile" required pattern="[0-9]{10}" class="form-input" placeholder="Enter mobile number" />
              </div>
            </div>
            <!-- Password Fields -->
            <div class="space-y-5">
              <div class="form-field">
                <label for="password" class="form-label">Password<span class="text-[#003820]">*</span></label>
                <div class="relative">
                  <input type="password" name="password" id="password" required  class="form-input pr-12" placeholder="Enter password" />
                  <button type="button" id="togglePassword" class="password-toggle-btn">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                    </svg>
                  </button>
                </div>
              </div>
              <div class="form-field">
                <label for="password_confirm" class="form-label">
                  Confirm Password<span class="text-[#003820]">*</span>
                </label>
                <div class="relative">
                  <input type="password" name="password_confirm" id="password_confirm" required class="form-input pr-12" placeholder="Confirm password" />
                  <button type="button" id="togglePasswordConfirm" class="password-toggle-btn">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                    </svg>
                  </button>
                </div>
              </div>
            </div>




            <!-- Address Fields -->
            <div id="addressFields" class="hidden space-y-5 bg-[#003820]/5 p-6 rounded-xl border border-[#003820]/10">
              <h3 class="text-lg font-semibold text-[#003820] mb-4">Address Information</h3>
              <div class="form-field">
              <div class="space-y-4">
                <div class="grid grid-cols-1 gap-4">
                <div class="form-field">
                  <label for="address_line1" class="form-label">Address Line 1<span class="text-[#003820]">*</span></label>
                    <input type="text" name="address_line1" id="address_line1" class="form-input" placeholder="Address Line 1" data-required="true" />
                  </div>
                  <div class="form-field">
                    <label for="address_line2" class="form-label">Address Line 2</label>
                    <input type="text" name="address_line2" id="address_line2" class="form-input" placeholder="Address Line 2" />
                  </div>
                  </div>
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div class="form-field">
                    <label for="city" class="form-label">City<span class="text-[#003820]">*</span></label>
                    <input type="text" name="city" id="city" class="form-input" placeholder="City" data-required="true" />
                  </div>
                  <div class="form-field">
                    <label for="state_province" class="form-label">State/Province<span class="text-[#003820]">*</span></label>
                    <input type="text" name="state_province" id="state_province" class="form-input" placeholder="State/Province" data-required="true" />
                  </div>
                  </div>
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div class="form-field">
                    <label for="postcode" class="form-label">Postal Code<span class="text-[#003820]">*</span></label>
                    <input type="text" name="postcode" id="postcode" class="form-input" placeholder="Postal Code" data-required="true" />
                </div>
                </div>
              </div>
              </div>
            </div>
          </div>

          <!-- Form Actions Section -->
          <div class="border-t border-[#003820]/10 pt-8 md:pt-12">
            <!-- Turnstile Container -->
            <div class="flex justify-center mb-8">
              <div class="cf-turnstile" data-sitekey="1x00000000000000000000AA" data-theme="light"></div>
            </div>

            <!-- Submit Button Container -->
            <div class="px-4 sm:px-6 flex justify-center">
              <button type="submit" class="submit-button">
                <span>Create Account</span>
                <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3"/>
                </svg>
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</Layout>

<script>

  interface Country {
    name: {
      common: string;
    };
    idd: {
      root?: string;
      suffixes?: string[];
    };
  }

  function validatePassword(password: string) {
    const hasUppercase = /[A-Z]/.test(password);
    const hasLowercase = /[a-z]/.test(password);
    const hasNumber = /\d/.test(password);
    const hasSpecialChar = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]+/.test(password);
    const isLongEnough = password.length >= 8;

    return {
      isValid: hasUppercase && hasLowercase && hasNumber && hasSpecialChar && isLongEnough,
      hasUppercase,
      hasLowercase,
      hasNumber,
      hasSpecialChar,
      isLongEnough
    };
  }

  document.addEventListener('DOMContentLoaded', function () {
    // Password validation feedback
    const password = document.getElementById('password') as HTMLInputElement;
    const passwordFeedback = document.createElement('div');
    passwordFeedback.className = 'text-sm mt-2 space-y-1';
    password.parentNode?.appendChild(passwordFeedback);

    password.addEventListener('input', function() {
      const validation = validatePassword(this.value);
      passwordFeedback.innerHTML = `
        <p class="${validation.isLongEnough ? 'text-green-600' : 'text-red-600'}">✓ At least 8 characters</p>
        <p class="${validation.hasUppercase ? 'text-green-600' : 'text-red-600'}">✓ One uppercase letter</p>
        <p class="${validation.hasLowercase ? 'text-green-600' : 'text-red-600'}">✓ One lowercase letter</p>
        <p class="${validation.hasNumber ? 'text-green-600' : 'text-red-600'}">✓ One number</p>
        <p class="${validation.hasSpecialChar ? 'text-green-600' : 'text-red-600'}">✓ One special character</p>
      `;
    });

    // Form submission validation
    document.getElementById('applicationForm')?.addEventListener('submit', function(e) {
      const userType = (document.getElementById('userType') as HTMLSelectElement).value;
      const password = (document.getElementById('password') as HTMLInputElement).value;
      const confirmPassword = (document.getElementById('password_confirm') as HTMLInputElement).value;
      const validation = validatePassword(password);
      const addressFields = document.getElementById('addressFields');

      if (!validation.isValid) {
      e.preventDefault();
      alert('Please ensure your password meets all requirements.');
      return;
      }

      if (password !== confirmPassword) {
      e.preventDefault();
      alert('Passwords do not match');
      return;
      }

      if (addressFields && !addressFields.classList.contains('hidden')) {
      const address_line1 = (document.getElementById('address_line1') as HTMLInputElement);
      const city = (document.getElementById('city') as HTMLInputElement);
      const state_province = (document.getElementById('state_province') as HTMLInputElement);
      const postcode = (document.getElementById('postcode') as HTMLInputElement);

      if (address_line1.dataset.required && !address_line1.value ||
        city.dataset.required && !city.value ||
        state_province.dataset.required && !state_province.value ||
        postcode.dataset.required && !postcode.value) {
        e.preventDefault();
        alert('Please fill in all required address fields');
        return;
      }
      }
    });


    const userType = document.getElementById('userType');
    const solicitorFields = document.getElementById('solicitorFields');
    const claimantFields = document.getElementById('claimantFields');

    userType?.addEventListener('change', function (e) {
      const value = (e.target as HTMLSelectElement).value;
      const addressFields = document.getElementById('addressFields');
      const solicitorFields = document.getElementById('solicitorFields');
      const claimantFields = document.getElementById('claimantFields');

      if (value === 'solicitor') {
      solicitorFields?.classList.remove('hidden');
      claimantFields?.classList.add('hidden');
      addressFields?.classList.remove('hidden');
      } else if (value === 'co-funder') {
      solicitorFields?.classList.add('hidden');
      claimantFields?.classList.add('hidden');
      addressFields?.classList.add('hidden');  // Hide address fields for co-funders
      } else if (value === 'claimant') {
      claimantFields?.classList.remove('hidden');
      solicitorFields?.classList.add('hidden');
      addressFields?.classList.remove('hidden');
      } else {
      solicitorFields?.classList.add('hidden');
      claimantFields?.classList.add('hidden');
      addressFields?.classList.add('hidden');
      }
    });


    // Country codes fetch
    fetch('https://restcountries.com/v3.1/all?fields=name,idd')
      .then(response => response.json())
      .then((data: Country[]) => {
        const select = document.getElementById('country-code');
        data.sort((a: Country, b: Country) => a.name.common.localeCompare(b.name.common));

        data.forEach((country: Country) => {
          if (country.idd.root && country.idd.suffixes) {
            const option = document.createElement('option');
            const phoneCode = `${country.idd.root}${country.idd.suffixes[0]}`;
            option.value = phoneCode;
            option.textContent = `${country.name.common} (${phoneCode})`;
            select?.appendChild(option);
          }
        });
      })
      .catch(error => console.error('Error fetching country data:', error));

    // Password toggle functionality
    function setupPasswordToggle(passwordId: string, toggleId: string) {
      const togglePassword = document.querySelector('#' + toggleId);
      const password = document.querySelector('#' + passwordId);

      togglePassword?.addEventListener('click', function (this: HTMLElement) {
        const type = password?.getAttribute('type') === 'password' ? 'text' : 'password';
        password?.setAttribute('type', type);

        const svg = this.querySelector('svg');
        if (svg) {
          svg.innerHTML = type === 'password'
            ? '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />'
            : '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21" />';
        }
      });
    }

    setupPasswordToggle('password', 'togglePassword');
    setupPasswordToggle('password_confirm', 'togglePasswordConfirm');
  });

  function validateForm(event: SubmitEvent): boolean {
    const form = event.target as HTMLFormElement;
    const password = form.querySelector('#password') as HTMLInputElement;
    const confirmPassword = form.querySelector('#password_confirm') as HTMLInputElement;

    if (password.value !== confirmPassword.value) {
      alert('Passwords do not match');
      return false;
    }

    const userType = form.querySelector('#userType') as HTMLSelectElement;
    if (userType.value === '') {
      alert('Please select an application type');
      return false;
    }

    return true;
  }
</script>

<script is:inline src="https://challenges.cloudflare.com/turnstile/v0/api.js" async defer></script>

<style>
  /* Custom form styling */
  .form-label {
    @apply block text-sm font-medium text-[#4a5568] mb-2; /* More readable label color */
  }

  .form-input,
  .form-select {
    @apply w-full px-4 py-3 rounded-md border border-[#cbd5e0] text-[#2d3748] bg-white focus:outline-none focus:ring-2 focus:ring-[#90cdf4] focus:border-[#90cdf4] transition-colors; /* Lighter background, more subtle borders/focus */
  }

  .form-input::placeholder {
    @apply text-[#a0aec0]; /* Lighter placeholder text */
  }

  .password-toggle-btn {
    @apply absolute right-3 top-[50%] -translate-y-[50%] inline-flex items-center justify-center w-8 h-8 text-[#718096] hover:text-[#4a5568] transition-colors cursor-pointer; /* More prominent password toggle */
  }

  /* Premium container */
  .container-premium {
    @apply relative overflow-hidden;
    background: rgba(255, 255, 255, 0.9); /* solid background for better readability on texture */
    border: 1px solid rgba(0, 56, 32, 0.08);
    border-radius: 1.25rem;  /* slightly smaller radius */
    backdrop-filter: blur(24px);
    transition: all 0.4s ease-in-out;
    box-shadow: 0 0.75rem 2.25rem rgba(0, 56, 32, 0.05);
  }

  .submit-button {
      @apply relative w-full h-12 flex items-center justify-center px-6 text-lg font-semibold text-white rounded-md;
      background-color: #003820;
      transition: all 0.3s ease;
    }

    .submit-button:hover {
      background-color: #005732;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 56, 32, 0.2);
    }

    .submit-button:active {
      transform: translateY(0);
      box-shadow: 0 2px 6px rgba(0, 56, 32, 0.2);
    }

  /*General Styles*/
  label {
    @apply transition-all duration-300;
  }
  h1 {
    @apply text-3xl lg:text-4xl font-bold mb-4 text-[#003820]; /* Darker, bolder header */
  }

  /*Animations*/
  .animate-float-slow {
    animation: float-slow 12s ease-in-out infinite;
  }

  @keyframes float-slow {
    0%, 100% { transform: translate(0, 0) scale(1) rotate(0deg); }
    50% { transform: translate(2%, 2%) scale(1.05) rotate(1deg); }
  }

  .animate-float-reverse {
    animation: float-slow 12s ease-in-out infinite reverse;
  }

  .bg-grid {
      background-image: linear-gradient(rgba(0, 56, 32, 0.03) 1px, transparent 1px), linear-gradient(90deg, rgba(0, 56, 32, 0.03) 1px, transparent 1px);
      background-size: 20px 20px;
      background-position: center center;
    }
  /* Mobile and desktop */
  .container-premium {
      @apply px-6 py-8 sm:px-10 lg:px-16;
    }

    @media (min-width: 1024px) {
      .container-premium {
        @apply px-12 py-10; /* increased padding desktop */
      }
    }
</style>