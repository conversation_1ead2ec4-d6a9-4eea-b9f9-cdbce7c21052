---
import Layout from '../layouts/Layout.astro';
---

<Layout title="Learn More About 3Pay - Subscriber Information">
	<div class="relative w-full overflow-hidden">
		<section class="relative min-h-screen flex items-center justify-center py-20 bg-[#003820]/5">
			<!-- Premium background effects -->
			<div class="absolute inset-0 overflow-hidden pointer-events-none">
				<div class="absolute top-0 left-1/4 w-[45rem] h-[45rem] bg-gradient-to-br from-[#003820]/10 to-[#003820]/5 rounded-full blur-[160px] mix-blend-soft-light animate-float-slow"></div>
				<div class="absolute bottom-0 right-1/4 w-[35rem] h-[35rem] bg-gradient-to-tr from-[#003820]/15 to-[#003820]/5 rounded-full blur-[128px] mix-blend-soft-light animate-float-reverse"></div>
				<div class="absolute inset-0 bg-grid opacity-[0.015] mix-blend-overlay"></div>
			</div>

			<div class="container mx-auto px-4 relative z-10 mt-20">
				<div class="max-w-4xl mx-auto">
					<h1 class="text-5xl font-bold text-[#003820] mb-12 text-center animate-fade-in">Become a 3Pay Co-Funder</h1>
					
					<div class="prose prose-lg max-w-none space-y-8">
						<div class="bg-white/60 backdrop-blur-sm rounded-2xl p-8 shadow-lg animate-slide-up">
							<p class="mb-6 leading-relaxed">To become a co-funder and unlock truly exceptional returns on your committed funds, you must first become a subscriber before you can become a co-funder and co-fund a claim.</p>
							
							<p class="mb-6">Becoming a subscriber is <span class="font-bold text-[#003820]">FREE</span>. Just complete the easy to do sign up process <a href="https://app.3payglobal.com" target="_blank" class="text-[#003820] font-semibold hover:underline transition-colors duration-300">HERE</a>.</p>
						</div>
						
						<div class="bg-white/60 backdrop-blur-sm rounded-2xl p-8 shadow-lg animate-slide-up animation-delay-200">
							<h2 class="text-2xl font-semibold text-[#003820] mb-4">Subscriber Benefits</h2>
							<p class="mb-4">Subscribers get FREE access to the 3Pay resource library of blogs and podcasts and other useful information about:</p>
							<ul class="list-none pl-0 mb-6 space-y-3">
								{[1, 2, 3].map((num) => (
									<li class="flex items-center space-x-3 p-3 bg-white/40 rounded-lg transform hover:scale-102 transition-all duration-300">
										<span class="flex-shrink-0 w-8 h-8 flex items-center justify-center rounded-full bg-[#003820] text-white">{num}</span>
										<span class="flex-1">
											{num === 1 ? "The litigation funding landscape in England" :
											 num === 2 ? "The 3Pay business model" :
											 "Litigation Funding with 3Pay"}
										</span>
									</li>
								))}
							</ul>
						</div>
						
						<div class="bg-white/60 backdrop-blur-sm rounded-2xl p-8 shadow-lg animate-slide-up animation-delay-400">
							<h2 class="text-2xl font-semibold text-[#003820] mb-4">Subscriber Levels</h2>
							<p class="mb-6">There are three subscriber levels, 1, 2, and 3, and a subscriber must access and complete the three levels before they can get access to available claims for funding.</p>
							
							<p class="mb-6">The three subscriber levels can be completed in no time at all, just by confirming with a tick that each of the twenty-three (23) blogs or podcasts have been read or listened to. This ensures the subscriber has become familiar with litigation funding and the 3Pay business model.</p>
						</div>
						
						<div class="bg-white/60 backdrop-blur-sm rounded-2xl p-8 shadow-lg animate-slide-up animation-delay-600">
							<h2 class="text-2xl font-semibold text-[#003820] mb-4">Investment Profile</h2>
							<p class="mb-6">As a subscriber, you will also need to disclose your attitude towards risk - low, medium, or high - and if you are a sophisticated investor or high net worth individual and what amount of available funds you can commit for a period up to 2 years in return for truly exceptional rewards.</p>
						</div>
						
						<div class="mt-12 text-center animate-bounce-subtle">
							<a href="https://app.3payglobal.com" target="_blank" 
								class="inline-flex items-center justify-center px-10 py-4 text-lg font-semibold text-white bg-[#003820] rounded-xl 
								hover:bg-[#004d2e] transition-all duration-300 transform hover:-translate-y-1 hover:shadow-lg
								group">
								SUBSCRIBE HERE
								<svg class="w-5 h-5 ml-2 transform group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
								</svg>
							</a>
						</div>
					</div>
				</div>
			</div>
		</section>
	</div>
</Layout>

<style>
	.animate-fade-in {
		opacity: 0;
		animation: fadeIn 1s ease-out forwards;
	}

	.animate-slide-up {
		opacity: 0;
		transform: translateY(20px);
		animation: slideUp 0.8s ease-out forwards;
	}

	.animation-delay-200 {
		animation-delay: 200ms;
	}

	.animation-delay-400 {
		animation-delay: 400ms;
	}

	.animation-delay-600 {
		animation-delay: 600ms;
	}

	.animate-bounce-subtle {
		animation: bounceSubtle 3s infinite;
	}

	@keyframes fadeIn {
		from {
			opacity: 0;
		}
		to {
			opacity: 1;
		}
	}

	@keyframes slideUp {
		from {
			opacity: 0;
			transform: translateY(20px);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}

	@keyframes bounceSubtle {
		0%, 100% {
			transform: translateY(0);
		}
		50% {
			transform: translateY(-5px);
		}
	}

	.hover\:scale-102:hover {
		transform: scale(1.02);
	}

	.bg-grid {
		background-size: 40px 40px;
		background-image: 
			linear-gradient(to right, rgba(0, 56, 32, 0.1) 1px, transparent 1px),
			linear-gradient(to bottom, rgba(0, 56, 32, 0.1) 1px, transparent 1px);
	}

	.animate-float-slow {
		animation: float 8s ease-in-out infinite;
	}

	.animate-float-reverse {
		animation: float 8s ease-in-out infinite reverse;
	}

	@keyframes float {
		0%, 100% {
			transform: translateY(0);
		}
		50% {
			transform: translateY(-20px);
		}
	}
</style>
