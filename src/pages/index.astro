---
import Layout from '@layouts/Layout.astro';
import Hero from '@components/Hero.astro';
import Workflow from '@components/Workflow.astro';
import Boost from '@components/Boost.astro';
import CTA from '@components/CTA.astro';
import Testimonials from '@components/Testimonials.astro';
import WhyFund from '@components/WhyFund.astro';
import Blog from '@components/Blog.astro';
import Footer from '@components/Footer.astro';
---

<Layout title="3Pay Global - Litigation Funding Platform">
  <main class="relative overflow-hidden pt-20">
    <!-- Hero Section -->
    <section class="relative bg-gradient-to-b from-white via-[#f0f7f4] to-white">
      <div class="absolute inset-0 bg-[url('/grid-pattern.svg')] opacity-10"></div>
      <div class="relative z-10" data-aos="fade-up" data-aos-duration="1000">
        <Hero />
      </div>
    </section>

    <!-- Workflow Section -->
    <section class="relative bg-white">
      <div class="absolute inset-0 bg-[#003820] opacity-[0.02] transform -skew-y-6"></div>
      <div class="relative z-10" data-aos="fade-up">
        <Workflow />
      </div>
    </section>

    <!-- Boost Section -->
    <section class="relative bg-gradient-to-r from-[#003820]/5 to-white">
      <div class="relative z-10" data-aos="fade-up">
        <Boost />
      </div>
    </section>

    <!-- CTA Section -->
    <section class="relative bg-[#003820]/[0.02]">
      <div class="relative z-10" data-aos="zoom-in">
        <CTA />
      </div>
    </section>

    <!-- Testimonials -->
    <section class="relative bg-white">
      <div class="relative z-10" data-aos="fade-up">
        <Testimonials />
      </div>
    </section>

    <!-- Why Fund Section -->
    <section class="relative bg-gradient-to-b from-white to-[#f0f7f4]">
      <div class="relative z-10" data-aos="fade-up">
        <WhyFund />
      </div>
    </section>

    <!-- Blog Section -->
    <section class="relative bg-gradient-to-b from-[#003820]/[0.02] to-white">
      <div class="relative z-10" data-aos="fade-up">
        <Blog />
      </div>
    </section>

   
  </main>
</Layout>

<style>
  main {
    position: relative;
  }
  
  main::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 100vh;
    background: radial-gradient(circle at 50% 50%, rgba(0, 56, 32, 0.03) 0%, transparent 50%);
    pointer-events: none;
  }

  section {
    overflow: hidden;
  }
</style>


