// To update pocketbase types
//npx pocketbase-typegen --db ~/projects/ttl/three_pay_platform/backend/pb_data/data.db --out ./src/data/pocketbase-types.ts
import nodemailer from 'nodemailer';
import type { ClaimsResponse, TypedPocketBase, UsersRecord } from './pocketbase-types'
import { getCurrentUserName, getSubscriberId } from '@lib/auth';


// Setup nodemailer transporter (you should configure this with your email service)
const transporter = nodemailer.createTransport({
  host: import.meta.env.THREEPAYEMAILHOST || process.env.THREEPAYEMAILHOST,
  port: 587,
  auth: {
    user: import.meta.env.THREEPAYEMAILUSERNAME || process.env.THREEPAYEMAILUSERNAME,
    pass: import.meta.env.THREEPAYEMAILPASS || process.env.THREEPAYEMAILPASS
  }
});

// export async function getArticles({ pb, level }: { pb: TypedPocketBase, level?: string }) {
//   const options = { filter: 'required_level ~ ""', sort: '-read_by' }

//   if (level) {
//     options.filter = `required_level ~ "${level as string}"`
//   }
//   const articles = await pb
//     .collection('articles')
//     .getFullList(options)

//   return articles
// }

// export async function getAllUsers(pb: TypedPocketBase) {
//   const options = { filter: 'user_type !~ "admin"', sort: '-created' }

//   const users = await pb
//     .collection('users')
//     .getFullList(options)

//   return users
// }

// export async function getAllSubscribers(pb: TypedPocketBase) {
//   const options = { sort: '-created', expand: 'user_id' }

//   const users = await pb
//     .collection('subscribers')
//     .getFullList(options)
//   return users
// }

// export async function getAllClaims(pb: TypedPocketBase) {
//   const options = { sort: '-created', expand: 'experts,solicitors,barristers,user_id,funded_by' }

//   const users = await pb
//     .collection('claims')
//     .getFullList(options)

//   return users
// }

// export async function getAllSolicitors(pb: TypedPocketBase) {
//   const options = { sort: '-created', expand: 'claims,user_id' }

//   const users = await pb
//     .collection('solicitors')
//     .getFullList(options)

//   return users
// }

// export async function getAllBarristers(pb: TypedPocketBase) {
//   const options = { sort: '-created', expand: 'claims' }

//   const users = await pb
//     .collection('barristers')
//     .getFullList(options)

//   return users
// }

// export async function updateBarrister(pb: TypedPocketBase, id: string, data: any) {
//   await pb.collection('barristers').update(id, data)
// }

// export async function getAllExpertWitnesses(pb: TypedPocketBase) {
//   const options = { sort: '-created', expand: 'claims' }

//   const users = await pb
//     .collection('experts')
//     .getFullList(options)

//   return users
// }

// export async function updateExpertWitness(pb: TypedPocketBase, id: string, data: any) {
//   await pb.collection('experts').update(id, data)
// }
// //@ts-expect-error
// export async function getClaim(pb: TypedPocketBase, claim_id?: string): Promise<ClaimsResponse<TexpandSolicitors, TexpandExperts>> {

//   if (claim_id) {
//     let claim: ClaimsResponse<TexpandSolicitors, TexpandExperts> = await pb.collection('claims').getFirstListItem(`id = '${claim_id as string}'`, { expand: 'solicitors,experts,barristers,user_id', });
//     // Format the created field
//     claim = {
//       ...claim,
//       created: formatDate(claim.created)
//     };
//     return claim;
//   }
// }
// export async function haveFundedClaim(pb: TypedPocketBase, claim_id: string, subscriber_id: string): Promise<boolean> {
//   let options = `claim_id = "" && subscriber_id ~ ""`

//   if (claim_id && subscriber_id) {
//     options = `id = "${claim_id as string}" && funded_by ~ "${subscriber_id as string}"`
//   }
//   try {
//     const funded = await pb.collection('claims').getFirstListItem(options)
//     return funded ? true : false;
//   } catch (error) {
//     return false;
//   }
// }

// export async function getClaims(pb: TypedPocketBase, user_id?: string, solicitor_id?: string): Promise<ClaimsResponse<TexpandSolicitors, TexpandExperts>[]> {

//   const options = { filter: 'user_id = ""', expand: 'solicitors,experts', sort: '-created' };

//   if (user_id !== undefined) {
//     options.filter = `user_id ~ "${user_id as string}"`;
//   }
//   if (solicitor_id !== undefined) {
//     options.filter = `solicitors ~ "${solicitor_id as string}"`
//   }
//   // console.log(options)
//   let claims: ClaimsResponse<TexpandSolicitors, TexpandExperts>[] = await pb
//     .collection('claims')
//     .getFullList(options);

//   // Format the created field
//   claims = claims.map(claim => ({
//     ...claim,
//     created: formatDate(claim.created)
//   }));
//   return claims;
//   // .sort(
//   //   (a, b) => getClaimInterest({ claim: a }) - getClaimInterest({ claim: b })
//   // )
// }

// export async function getOpenClaims(pb: TypedPocketBase) {
//   const options = { filter: 'status = "approved"' };

//   let claims: ClaimsResponse[] = await pb
//     .collection('claims')
//     .getFullList(options);

//   // Format the created field
//   claims = claims.map(claim => ({
//     ...claim,
//     created: formatDate(claim.created)
//   }));
//   return claims;
// }

// export async function getFundedClaims(pb: TypedPocketBase, subscriber_id?: string) {
//   const options = { filter: 'funded_by = ""' };

//   if (subscriber_id) {
//     options.filter = `funded_by ~ "${subscriber_id as string}"`;
//   }

//   let claims: ClaimsResponse[] = await pb
//     .collection('claims')
//     .getFullList(options);

//   // Format the created field
//   claims = claims.map(claim => ({
//     ...claim,
//     created: formatDate(claim.created)
//   }));
//   return claims;
//   // .sort(
//   //   (a, b) => getClaimInterest({ claim: a }) - getClaimInterest({ claim: b })
//   // )
// }

// export async function getTransactions(pb: TypedPocketBase, subscriber_id?: string) {
//   const options = { filter: 'subscriber = ""', sort: '-created' };

//   if (subscriber_id) {
//     options.filter = `subscriber = "${subscriber_id as string}"`;
//   }

//   let payments: PaymentResponse[] = await pb
//     .collection('payments')
//     .getFullList(options);

//   return payments;

// }

// function formatDate(dateString: string): string {
//   const date = new Date(dateString);
//   const options: Intl.DateTimeFormatOptions = {
//     year: 'numeric',
//     month: 'long',
//     day: 'numeric'
//   };
//   return date.toLocaleString('en-US', options);
// }

// export async function getClaimant(pb: TypedPocketBase, user_id: string) {
//   let options = `user_id = ""`

//   if (user_id) {
//     options = `user_id = "${user_id as string}"`
//   }
//   const claimant = await pb
//     .collection('claims')
//     .getFirstListItem(options)
//   return claimant
// }

// export async function getArticle(pb: TypedPocketBase, article_id: string) {
//   let options = `article_id = ""`

//   if (article_id) {
//     options = `id = "${article_id as string}"`
//   }
//   const article = await pb
//     .collection('articles')
//     .getFirstListItem(options)
//   return article
// }

// export async function isArticleRead(pb: TypedPocketBase, user_id: string, article_id: string) {

//   let options = `user_id = ""`;

//   if (article_id) {
//     options = `id = "${user_id as string}" && read_articles ~ "${article_id as string}"`;
//   }
//   try {
//     const article = await pb.collection('subscribers').getFirstListItem(options);
//     return article ? true : false;
//   } catch (error) {
//     return false;
//   }
// }

// export async function getReadArticles(pb: TypedPocketBase, user_id: string) {

//   let options = { filter: `user_id = ""` };

//   if (user_id) {
//     options.filter = `id = "${user_id as string}"`;
//   }

//   try {
//     const articles = await pb.collection('subscribers').getFullList(options);
//     const reads = articles.map((article: any) => { return article.read_articles })
//     const flatArray = reads.flat();
//     return flatArray
//   } catch (error) {
//     // console.error('Error fetching article read status:', error);
//     return [];
//   }
// }

// export async function updateArticle(
//   pb: TypedPocketBase,
//   id: string,
//   data: any
// ) {
//   await pb.collection('articles').update(id, data)

// }

// export async function updateClaim(
//   pb: TypedPocketBase,
//   id: string,
//   data: any
// ) {
//   await pb.collection('claims').update(id, data)
// }

// export async function updateReadAricles(
//   pb: TypedPocketBase,
//   id: string,
//   data: any
// ) {
//   await pb.collection('subscribers').update(id, data)
// }

// export async function updateSubscriber(
//   pb: TypedPocketBase,
//   id: string,
//   data: any
// ) {
//   await pb.collection('subscribers').update(id, data)
// }

// export async function sendArticleReadEmail(
//   pb: TypedPocketBase,
//   id: string,
//   to: string,
// ) {
//   const article = await pb.collection('articles').getOne(id);

//   const htmlContent = `
//       <!DOCTYPE html>
//       <html lang="en">
//       <head>
//         <meta charset="UTF-8">
//         <meta name="viewport" content="width=device-width, initial-scale=1.0">
//         <title>Congratulations on Completing the Article!</title>
//         <style>
//           body { 
//             font-family: Arial, sans-serif; 
//             line-height: 1.6; 
//             color: #333; 
//             max-width: 600px; 
//             margin: 0 auto; 
//             padding: 20px;
//           }
//           .header { 
//             background-color: #064F32; 
//             color: white; 
//             padding: 20px; 
//             text-align: center; 
//             border-radius: 5px 5px 0 0;
//           }
//           .content { 
//             background-color: #f9f9f9; 
//             padding: 20px; 
//             border-radius: 0 0 5px 5px;
//           }
//           .button {
//             display: inline-block;
//             padding: 10px 20px;
//             background-color: #064F32;
//             color: white;
//             text-decoration: none;
//             border-radius: 5px;
//             margin-top: 5px;
//           }
//           .explore{
//             color: white;
//           }
//           .footer {
//             text-align: center;
//             margin-top: 20px;
//             font-size: 0.8em;
//             color: #666;
//           }
//         </style>
//       </head>
//       <body>
//         <div class="header">
//           <h1>🎉 Congratulations! 🎉</h1>
//         </div>
//         <div class="content">
//           <p>Hello ${await getCurrentUserName(pb)},</p>
//           <p>We're thrilled to congratulate you on completing the article:</p>
//           <h2>"${article.name}"</h2>
//           <p>Your commitment to expanding your knowledge is truly commendable. By reading this article, you've taken an important step in your journey of understanding litigation funding and its potential impacts.</p>
//           <!-- <p>Here are some key takeaways from the article you've just read:</p>
//           <ul>
//             <li>Insight into the mechanics of litigation funding</li>
//             <li>Understanding of the potential benefits and risks</li>
//             <li>Awareness of how this could apply to your specific situation</li>
//           </ul>-->
//           <p>We hope this article has provided you with valuable insights and has piqued your interest in learning more about the world of litigation funding.</p>
//           <p>We have more insightful articles waiting for you!</p>
//           <a href="https://3payglobal.com/app/inbox" class="button"><span class="explore">Explore More Articles<span></a>
//           <p>Thank you for your engagement and trust in 3Pay Global. We're here to support you every step of the way in your journey through the simplicity of litigation funding.</p>
//           <p>Best regards,<br>The 3Pay Global Team</p>
//         </div>
//         <div class="footer">
//           <p>© 2025 3Pay Global. All rights reserved.</p>
//           <p>If you have any questions, please don't hesitate to <a href="mailto:<EMAIL>">contact us</a> or visit our <a href="https://3payglobal.com/contact/" >contact</a> page.</p>
//         </div>
//       </body>
//       </html>
//     `;

//   try {
//     await transporter.sendMail({
//       from: `"3Pay Global" <${import.meta.env.THREEPAYEMAILUSERNAME || process.env.THREEPAYEMAILUSERNAME}>`,
//       to: to,
//       subject: `Congratulations on Completing "${article.name}"!`,
//       html: htmlContent,
//       priority: "high",
//     });
//     console.log('Congratulatory email sent successfully');
//   } catch (error) {
//     console.error('Error sending congratulatory email:', error);
//   }
//   // return article;
// }

// export async function sendClaimFundedEmail(
//   pb: TypedPocketBase,
//   id: string,
//   to: string,
// ) {
//   const claim = await pb.collection('claims').getOne(id);
//   const subscriber_id = await getSubscriberId(pb)

//   const htmlContent = `
//      <!DOCTYPE html>
// <html lang="en">
// <head>
//     <meta charset="UTF-8">
//     <meta name="viewport" content="width=device-width, initial-scale=1.0">
//     <title>3Pay Global Bank Account Details</title>
//     <script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>
    
//     <style>
//         body {
//             font-family: Arial, sans-serif;
//             background-color: #f3f4f6;
//             margin: 0;
//             padding: 0;
//         }
//         .container {
//             max-width: 1200px;
//             margin: 0 auto;
//             padding: 2rem 1rem;
//         }
//         .header { 
//             background-color: #064F32; 
//             color: white; 
//             padding: 20px; 
//             text-align: center; 
//             border-radius: 5px 5px 0 0;
//         }
//         .alert {
//             display: flex;
//             padding: 1rem;
//             margin-bottom: 1rem;
//             background-color: #fef3c7;
//             border-top: 4px solid #f59e0b;
//         }
//         .alert-icon {
//             flex-shrink: 0;
//             color: #b45309;
//         }
//         .alert-content {
//             margin-left: 0.75rem;
//             font-size: 0.875rem;
//         }
//         .alert-title {
//             font-size: 1.125rem;
//             font-weight: 600;
//             margin-bottom: 0.5rem;
//             color: #1d4ed8;
//         }
//         .alert-text {
//             color: #1e40af;
//             margin-bottom: 0.75rem;
//         }
//         .alert-list {
//             list-style-type: decimal;
//             padding-left: 1.5rem;
//             color: #1e40af;
//         }
//         .alert-list li {
//             margin-bottom: 0.5rem;
//         }
//         .card {
//             background-color: white;
//             box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
//             border-radius: 0.5rem;
//             padding: 1.5rem;
//             max-width: 42rem;
//             margin: 2rem auto;
//         }
//         .card-title {
//             font-size: 1.5rem;
//             font-weight: bold;
//             margin-bottom: 1rem;
//             text-align: center;
//             color: #1f2937;
//         }
//         .section {
//             margin-bottom: 1.5rem;
//         }
//         .section-title {
//             font-size: 1.125rem;
//             font-weight: 600;
//             margin-bottom: 0.5rem;
//             color: #374151;
//         }
//         .detail-list {
//             list-style-type: none;
//             padding: 0;
//             color: #4b5563;
//         }
//         .detail-list li {
//             margin-bottom: 0.5rem;
//         }
//         .detail-label {
//             font-weight: 500;
//         }
//         .footer {
//             text-align: center;
//             margin-top: 20px;
//             font-size: 0.8em;
//             color: #666;
//         }
//         a {
//             color: #1d4ed8;
//             text-decoration: underline;
//         }
//     </style>
// </head>
// <body>
//     <div class="container">
//         <div class="header">
//             <h1>🎉 Congratulations! 🎉</h1>
//         </div>
//         <p>Hello ${await getCurrentUserName(pb)},</p>
//         <p>We're thrilled to congratulate your commitment to fund claim with id ${claim.id}:</p>
        
//         <p>Thank you for your engagement and trust in 3Pay Global. We're here to support you every step of the way in your journey through the simplicity of litigation funding.</p>
//         <p>Best regards,<br>The 3Pay Global Team</p>

//         <div class="alert">
//             <i class="fas fa-triangle-exclamation alert-icon"></i>
//             <div class="alert-content">
//                 <h3 class="alert-title">Instructions for Co-funders</h3>
//                 <p class="alert-text">To successfully fund the claim, please follow these steps:</p>
//                 <ol class="alert-list">
//                    <li>Choose the appropriate paymen method (Domestic SWIFT, or SEPA) based on you location and banking capabilities.</li>
//                    <li>Use the corresponding bank account details provided below for your chosen payment method.</li>
//                    <li>Ensure that you include your unique co-funder reference number i.e (<span class="font-bold">${subscriber_id}</span>) and claim reference number i.e (<span class="font-bold">${claim.id}</span>) in the payment description or reference field.</li>
//                    <li>Send a copy of payment receipt to <a href="mailto:<EMAIL>"><span class="underline"><EMAIL></span></a> </li></li>
//                    <li>Double-check all details before confirming the transfer to avoid any delays or issues with your payment.</li>
//                 </ol>
//                 <p class="alert-text">If you have any questions or encounter any issues, please contact our finance team at <a href="mailto:<EMAIL>"><EMAIL></a>.</p>
//             </div>
//         </div>

//         <div class="card">
//             <h2 class="card-title">3Pay Global Bank Account Details</h2>

//             <div class="section">
//                 <h3 class="section-title">Domestic Details</h3>
//                 <ul class="detail-list">
//                     <li><span class="detail-label">Account type:</span> Business</li>
//                     <li><span class="detail-label">Account name:</span> 3PAY GLOBAL LTD</li>
//                     <li><span class="detail-label">Sort code:</span> 04-03-70</li>
//                     <li><span class="detail-label">Account number:</span> ********</li>
//                     <li><span class="detail-label">Address:</span> 78 York Street, London, W1H 1DP</li>
//                     <li><span class="detail-label">Bank/Institution:</span> Payrnet</li>
//                     <li><span class="detail-label">Bank address:</span> Po Box 1130, Cardiff, CF11 1WF</li>
//                 </ul>
//             </div>

//             <div class="section">
//                 <h3 class="section-title">International Details for SWIFT Payments</h3>
//                 <ul class="detail-list">
//                     <li><span class="detail-label">Account name:</span> 3PAY GLOBAL LTD</li>
//                     <li><span class="detail-label">IBAN:</span> **********************</li>
//                     <li><span class="detail-label">SWIFT/BIC:</span> TCCLGB3L</li>
//                     <li><span class="detail-label">Address:</span> 78 York Street, London, W1H 1DP</li>
//                     <li><span class="detail-label">Bank/Institution:</span> The Currency Cloud Limited</li>
//                     <li><span class="detail-label">Bank address:</span> The Steward Building 12 Steward Street, London, E1 6FQ, GB</li>
//                     <li><span class="detail-label">Intermediary bank SWIFT:</span> BARCGB22 (only use if required by sender bank)</li>
//                 </ul>
//             </div>

//             <div class="section">
//                 <h3 class="section-title">International Details for SEPA Payments</h3>
//                 <ul class="detail-list">
//                     <li><span class="detail-label">Account name:</span> 3PAY G LTD</li>
//                     <li><span class="detail-label">SEPA IBAN:</span> **********************</li>
//                     <li><span class="detail-label">SEPA SWIFT/BIC:</span> TCCLGB31</li>
//                     <li><span class="detail-label">Address:</span> 78 York Street, London, W1H 1DP</li>
//                     <li><span class="detail-label">Bank/Institution:</span> The Currency Cloud Limited</li>
//                     <li><span class="detail-label">Bank address:</span> The Steward Building 12 Steward Street, London, E1 6FQ, GB</li>
//                 </ul>
//             </div>
//         </div>

//         <div class="footer">
//             <p>© 2025 3Pay Global. All rights reserved.</p>
//             <p>If you have any questions, please don't hesitate to <a href="mailto:<EMAIL>">contact us</a> or visit our <a href="https://3payglobal.com/contact/">contact</a> page.</p>
//         </div>
//     </div>
// </body>
// </html>
//     `;

//   try {
//     await transporter.sendMail({
//       from: `"3Pay Global" <${import.meta.env.THREEPAYEMAILUSERNAME || process.env.THREEPAYEMAILUSERNAME}>`,
//       to: to,
//       subject: `Notice of intent to fund claim with id ${claim.id}`,
//       html: htmlContent,
//       priority: "high",
//     });
//     console.log('Congratulatory email sent successfully');
//   } catch (error) {
//     console.error('Error sending congratulatory email:', error);
//   }
//   // return article;
// }

// export async function sendNewUserEmail(
//   pb: TypedPocketBase,
//   id: string,
//   role: string
// ) {

//   const htmlContent = `
//      <!DOCTYPE html>
// <html lang="en">
// <head>
//     <meta charset="UTF-8">
//     <meta name="viewport" content="width=device-width, initial-scale=1.0">
//     <title>New User Sign Up</title>
//     <script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>
    
//     <style>
//         body {
//             font-family: Arial, sans-serif;
//             background-color: #f3f4f6;
//             margin: 0;
//             padding: 0;
//         }
//         .container {
//             max-width: 1200px;
//             margin: 0 auto;
//             padding: 2rem 1rem;
//         }
//         .header { 
//             background-color: #064F32; 
//             color: white; 
//             padding: 20px; 
//             text-align: center; 
//             border-radius: 5px 5px 0 0;
//         }
//         .alert {
//             display: flex;
//             padding: 1rem;
//             margin-bottom: 1rem;
//             background-color: #fef3c7;
//             border-top: 4px solid #f59e0b;
//         }
//         .alert-icon {
//             flex-shrink: 0;
//             color: #b45309;
//         }
//         .alert-content {
//             margin-left: 0.75rem;
//             font-size: 0.875rem;
//         }
//         .alert-title {
//             font-size: 1.125rem;
//             font-weight: 600;
//             margin-bottom: 0.5rem;
//             color: #1d4ed8;
//         }
//         .alert-text {
//             color: #1e40af;
//             margin-bottom: 0.75rem;
//         }
//         .alert-list {
//             list-style-type: decimal;
//             padding-left: 1.5rem;
//             color: #1e40af;
//         }
//         .alert-list li {
//             margin-bottom: 0.5rem;
//         }
//         .card {
//             background-color: white;
//             box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
//             border-radius: 0.5rem;
//             padding: 1.5rem;
//             max-width: 42rem;
//             margin: 2rem auto;
//         }
//         .card-title {
//             font-size: 1.5rem;
//             font-weight: bold;
//             margin-bottom: 1rem;
//             text-align: center;
//             color: #1f2937;
//         }
//         .section {
//             margin-bottom: 1.5rem;
//         }
//         .section-title {
//             font-size: 1.125rem;
//             font-weight: 600;
//             margin-bottom: 0.5rem;
//             color: #374151;
//         }
//         .detail-list {
//             list-style-type: none;
//             padding: 0;
//             color: #4b5563;
//         }
//         .detail-list li {
//             margin-bottom: 0.5rem;
//         }
//         .detail-label {
//             font-weight: 500;
//         }
//         .footer {
//             text-align: center;
//             margin-top: 20px;
//             font-size: 0.8em;
//             color: #666;
//         }
//         a {
//             color: #1d4ed8;
//             text-decoration: underline;
//         }
//     </style>
// </head>
// <body>
//     <div class="container">
//         <div class="header">
//             <h1>🔔  New User Alert 🔔 </h1>
//         </div>
//         <p>Hi,</p>
//         <p>A new user with ID ${id} with the role of ${role} has signed up.
//         <p>Please review the account and take appropraite action on the Admin Dashboard</p>
        
//         <p>The 3Pay Global Team</p>

//         <div class="footer">
//             <p>© ${new Date().getFullYear()} 3Pay Global. All rights reserved.</p>
//             <p>If you have any challenges, please don't hesitate to <a href="mailto:<EMAIL>">contact Admin</a></p>
//         </div>
//     </div>
// </body>
// </html>
//     `;

//   try {
//     await transporter.sendMail({
//       from: `"3Pay Global" <${import.meta.env.THREEPAYEMAILUSERNAME || process.env.THREEPAYEMAILUSERNAME}>`,
//       to: '<EMAIL>',
//       subject: `New User Sign Up, ID#: ${id} with ${role} role`,
//       html: htmlContent,
//       priority: "high",
//     });
//     console.log('New Sign up email sent successfully');
//   } catch (error) {
//     console.error('Error sending congratulatory email:', error);
//   }
//   // return article;
// }

// export async function sendAccountStatusEmail(
//   pb: TypedPocketBase,
//   name: string,
//   role: string,
//   decision: string,
//   to: string
// ) {

//   const htmlContent = `
//      <!DOCTYPE html>
// <html lang="en">
// <head>
//     <meta charset="UTF-8">
//     <meta name="viewport" content="width=device-width, initial-scale=1.0">
//     <title>3Pay Global Account Status</title>
//     <script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>
    
//     <style>
//         body {
//             font-family: Arial, sans-serif;
//             background-color: #f3f4f6;
//             margin: 0;
//             padding: 0;
//         }
//         .container {
//             max-width: 1200px;
//             margin: 0 auto;
//             padding: 2rem 1rem;
//         }
//         .header { 
//             background-color: #064F32; 
//             color: white; 
//             padding: 20px; 
//             text-align: center; 
//             border-radius: 5px 5px 0 0;
//         }
//         .alert {
//             display: flex;
//             padding: 1rem;
//             margin-bottom: 1rem;
//             background-color: ${decision === 'approved' ? '#d1fae5' : '#fee2e2'};
//             border-top: 4px solid ${decision === 'approved' ? '#10b981' : '#ef4444'};
//         }
//         .alert-icon {
//             flex-shrink: 0;
//             color: ${decision === 'approved' ? '#059669' : '#dc2626'};
//         }
//         .alert-content {
//             margin-left: 0.75rem;
//             font-size: 0.875rem;
//         }
//         .alert-title {
//             font-size: 1.125rem;
//             font-weight: 600;
//             margin-bottom: 0.5rem;
//             color: ${decision === 'approved' ? '#059669' : '#dc2626'};
//         }
//         .alert-text {
//             color: ${decision === 'approved' ? '#065f46' : '#991b1b'};
//             margin-bottom: 0.75rem;
//         }
//         .card {
//             background-color: white;
//             box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
//             border-radius: 0.5rem;
//             padding: 1.5rem;
//             max-width: 42rem;
//             margin: 2rem auto;
//         }
//         .card-title {
//             font-size: 1.5rem;
//             font-weight: bold;
//             margin-bottom: 1rem;
//             text-align: center;
//             color: #1f2937;
//         }
//         .footer {
//             text-align: center;
//             margin-top: 20px;
//             font-size: 0.8em;
//             color: #666;
//         }
//         a {
//             color: #1d4ed8;
//             text-decoration: underline;
//         }
//     </style>
// </head>
// <body>
//     <div class="container">
//         <div class="header">
//             <h1>3Pay Global Account Status</h1>
//         </div>
//         <div class="card">
//             <div class="alert">
//                 <div class="alert-icon">
//                     <i class="fas ${decision === 'approved' ? 'fa-check-circle' : 'fa-exclamation-circle'}"></i>
//                 </div>
//                 <div class="alert-content">
//                     <h3 class="alert-title">Account ${decision === 'approved' ? 'Approved' : 'Not Approved'}</h3>
//                     <p class="alert-text">
//                         ${decision === 'approved' 
//                           ? 'Congratulations! Your account has been approved.' 
//                           : 'We regret to inform you that your account has not been approved at this time.'}
//                     </p>
//                 </div>
//             </div>
//             <p>Hi ${name},</p>
//             <p>Your user account with role of ${role} has been ${decision}.</p>
//             ${decision === 'approved' 
//               ? `<p>You can now access your dashboard on the <a href="https://app.3payglobal.com">3Pay Global</a> website.</p>`
//               : `<p>If you have any questions about this decision, please contact our support team.</p>`
//             }
//             <p>The 3Pay Global Team</p>
//         </div>
//         <div class="footer">
//             <p>© ${new Date().getFullYear()} 3Pay Global. All rights reserved.</p>
//             <p>If you have any challenges, please don't hesitate to <a href="mailto:<EMAIL>">contact Support</a></p>
//         </div>
//     </div>
// </body>
// </html>
//     `;

//   try {
//     await transporter.sendMail({
//       from: `"3Pay Global" <${import.meta.env.THREEPAYEMAILUSERNAME || process.env.THREEPAYEMAILUSERNAME}>`,
//       to: to,
//       subject: `Your 3Pay Account has status`,
//       html: htmlContent,
//       priority: "high",
//     });
//     console.log('User account status email sent successfully');
//   } catch (error) {
//     console.error('Error sending account status email:', error);
//   }
//   // return article;
// }

// export async function createPayment(
//   pb: TypedPocketBase,
//   data: any
// ) {
//   await pb.collection('payments').create(data)
// }
// export async function getPayment(
//   pb: TypedPocketBase,
//   subscriber_id: string
// ) {
//   try{
//     const payment: PaymentResponse = await pb.collection('payments').getFirstListItem(`subscriber="${subscriber_id}"`)
//     return payment
//   }catch(e){
//     console.log(e)
//   }
// }

// export async function createSolicitor(
//   pb: TypedPocketBase,
//   data: any
// ) {
//   await pb.collection('solicitors').create(data)
// }
// export async function createBarrister(
//   pb: TypedPocketBase,
//   data: any
// ) {
//   await pb.collection('barristers').create(data)
// }
// export async function createArticle(
//   pb: TypedPocketBase,
//   data: any
// ) {
//   await pb.collection('articles').create(data)
// }

// export async function createL3Questionare(
//   pb: TypedPocketBase,
//   data: any
// ) {
//   await pb.collection('level3_questionaire').create(data)
// }

// export async function createExpert(
//   pb: TypedPocketBase,
//   data: any
// ) {
//   await pb.collection('experts').create(data)
// }

// export async function getExpert(pb: TypedPocketBase, email?: string) {

//   if (email) {
//     try {
//       const expert: SolicitorsResponse = await pb.collection('experts').getFirstListItem(`email="${email}"`)
//       return expert
//     } catch (error) {
//       console.error('Error fetching user:', error);
//       return null;
//     }
//   }
// }

// export async function updatePayment(
//   pb: TypedPocketBase,
//   id: string,
//   data: any
// ) {
//   await pb.collection('payments').update(id, data)
// }

// export async function createQuizResponses(pb: TypedPocketBase, data: {}) {

//   await pb.collection('quiz_responses').create(data)
// }

// export async function updateQuizResponses(
//   pb: TypedPocketBase,
//   id: string,
//   data: any
// ) {
//   await pb.collection('quiz_responses').update(id, data)
// }

// export async function getQuestions(pb: TypedPocketBase) {
//   let options = { sort: 'question_number' };
//   return await pb.collection('level3_questionaire').getFullList(options)
// }

// export async function getUserResponses(pb: TypedPocketBase, subscriber_id: string) {
//   let options = `subscriber_id = ""`;

//   if (subscriber_id) {
//     options = `subscriber_id = "${subscriber_id as string}"`
//   }
//   try {
//     const responses = await pb.collection('quiz_responses').getFirstListItem(options)
//     return responses
//   } catch (e) {
//     console.log(e)
//     return null;
//   }

// }

// export async function getSolicitor(pb: TypedPocketBase, id?: string, name?: string, email?: string) {

//   if (id) {
//     try {
//       const solicitor: SolicitorsResponse = await pb.collection('solicitors').getOne(id)
//       return solicitor
//     } catch (error) {
//       console.error('Error fetching user:', error);
//       return null;
//     }
//   }
//   if (name) {
//     try {
//       const solicitor: SolicitorsResponse = await pb.collection('solicitors').getFirstListItem(`full_name="${name}"`)
//       return solicitor
//     } catch (error) {
//       console.error('Error fetching user:', error);
//       return null;
//     }
//   }
//   if (email) {

//     try {
//       const solicitor: SolicitorsResponse = await pb.collection('users').getFirstListItem(`email="${email}"`)
//       return solicitor
//     } catch (error) {
//       console.error('Error fetching user:', error);
//       return null;
//     }
//   }
// }

// export async function updateSolicitor(pb: TypedPocketBase, id: string, data: any) {
//   await pb.collection('solicitors').update(id, data)
// }

// export async function updateUser(pb: TypedPocketBase, id: string, data: any) {
//   await pb.collection('users').update(id, data)
// }

// export function processImages(pb: TypedPocketBase, subscriber: SubscribersResponse) {
//   // need to replace internal url with public url for the internet to see
//   // railway uses internal but we switch to external just before showing to the internet
//   // this function takes subscriberResponse meaning it only gets from the db not write so no harm to the db.

//   const pocketbasePublicUrlRailway: string =
//     import.meta.env.POCKETBASE_PUBLIC_URL || process.env.POCKETBASE_PUBLIC_URL;
//   const pocketbaseInternal: string =
//     import.meta.env.POCKERBASE_URL || process.env.POCKETBASE_URL;

//   type ImageItem = {
//     name: string;
//     url: string;
//     url_larger: string;
//   };

//   const images: ImageItem[] = [];

//   subscriber?.id_card?.map((image: string) => {
//     images.push({
//       name: image,
//       url: pb.files
//         .getUrl(subscriber, image, {
//           thumb: '0x200',
//         }),
//       // .replace(pocketbaseInternal, pocketbasePublicUrlRailway),
//       url_larger: pb.files
//         .getUrl(subscriber, image, {
//           thumb: '0x800',
//         })
//       // .replace(pocketbaseInternal, pocketbasePublicUrlRailway),
//     });
//   });
//   return images;
// }

// export function processPORDocuments(pb: TypedPocketBase, subscriber: SubscribersResponse) {
//   const pocketbasePublicUrlRailway: string =
//     import.meta.env.POCKETBASE_PUBLIC_URL || process.env.POCKETBASE_PUBLIC_URL;
//   const pocketbaseInternal: string =
//     import.meta.env.POCKERBASE_URL || process.env.POCKETBASE_URL;

//   type DocumentItem = {
//     name: string;
//     url: string;
//     url_larger?: string;
//     type: 'image' | 'pdf';
//   };

//   const documents: DocumentItem[] = [];

//   subscriber?.proof_of_residence?.forEach((document: string) => {
//     const fileExtension = document.split('.').pop()?.toLowerCase();
//     const isPDF = fileExtension === 'pdf';

//     const baseItem: DocumentItem = {
//       name: document,
//       url: pb.files.getUrl(subscriber, document),
//       type: isPDF ? 'pdf' : 'image',
//     };

//     if (!isPDF) {
//       baseItem.url = pb.files.getUrl(subscriber, document,
//         { thumb: '0x200' });
//       baseItem.url_larger = pb.files.getUrl(subscriber, document, { thumb: '0x800' });
//     }

//     // Uncomment these lines if you need to replace internal URLs with public URLs
//     // baseItem.url = baseItem.url.replace(pocketbaseInternal, pocketbasePublicUrlRailway);
//     // if (baseItem.url_larger) {
//     //   baseItem.url_larger = baseItem.url_larger.replace(pocketbaseInternal, pocketbasePublicUrlRailway);
//     // }

//     documents.push(baseItem);
//   });

//   return documents;
// }

// export function processClaimDocuments(pb: TypedPocketBase, claim: ClaimsResponse) {
//   const pocketbasePublicUrlRailway: string =
//     import.meta.env.POCKETBASE_PUBLIC_URL || process.env.POCKETBASE_PUBLIC_URL;
//   const pocketbaseInternal: string =
//     import.meta.env.POCKERBASE_URL || process.env.POCKETBASE_URL;

//   type DocumentItem = {
//     name: string;
//     url: string;
//     url_larger?: string;
//     type: 'image' | 'pdf' | 'word' | 'excel';
//   };

//   const documents: DocumentItem[] = [];

//   claim?.documents?.forEach((document: string) => {
//     const fileExtension = document.split('.').pop()?.toLowerCase();
    
//     // Determine file type
//     let fileType: DocumentItem['type'];
//     switch (fileExtension) {
//       case 'pdf':
//         fileType = 'pdf';
//         break;
//       case 'doc':
//       case 'docx':
//         fileType = 'word';
//         break;
//       case 'xls':
//       case 'xlsx':
//         fileType = 'excel';
//         break;
//       case 'jpg':
//       case 'jpeg':
//       case 'png':
//         fileType = 'image';
//         break;
//       default:
//         fileType = 'pdf'; // Default fallback
//     }

//     const baseItem: DocumentItem = {
//       name: document,
//       url: pb.files.getUrl(claim, document),
//       type: fileType,
//     };

//     // Only generate thumbnails for image files
//     if (fileType === 'image') {
//       baseItem.url = pb.files.getUrl(claim, document, { thumb: '0x200' });
//       baseItem.url_larger = pb.files.getUrl(claim, document, { thumb: '0x800' });
//     }

//     // Uncomment these lines if you need to replace internal URLs with public URLs
//     // baseItem.url = baseItem.url.replace(pocketbaseInternal, pocketbasePublicUrlRailway);
//     // if (baseItem.url_larger) {
//     //   baseItem.url_larger = baseItem.url_larger.replace(pocketbaseInternal, pocketbasePublicUrlRailway);
//     // }

//     documents.push(baseItem);
//   });

//   return documents;
// }

// export function getClaimInterest({ claim }: { claim: ClaimsResponse }) {
//   switch (claim.interest) {
//     case "STAGE 1: Pre-Action":
//       return 75
//     case "STAGE 2: Letter Before Action":
//       return 70
//     case "STAGE 3: Claim Issued and Served":
//       return 65
//     case "STAGE 4: Particulars of Claim Served":
//       return 60
//     case "STAGE 5: Defence Received":
//       return 55
//     case "STAGE 6: Case Management Court Hearing":
//       return 50
//     case "STAGE 7: Directions Hearings (Court)":
//       return 45
//     case "STAGE 8: Applications Hearings":
//       return 40
//     case "STAGE 9: Witness Statements":
//       return 35
//     case "STAGE 10: Expert Reports":
//       return 30
//     case "STAGE 11: Disclosure - Evidence":
//       return 25
//     case "STAGE 12: Inspections":
//       return 20
//     case "STAGE 13: Pre-Trial Review":
//       return 15
//     case "STAGE 14: Trial Preparations":
//       return 10
//     case "STAGE 15: Part 36/Offers/Mediation":
//       return 5
//     default:
//       return 0;
//   }
// }
