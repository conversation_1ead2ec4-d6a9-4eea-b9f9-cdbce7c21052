/* Base animations */
[data-aos] {
	opacity: 0;
	transform: translateZ(0);
	backface-visibility: hidden;
	perspective: 1000px;
	will-change: transform, opacity;
}

[data-aos].aos-animate {
	opacity: 1;
	transform: none;
}

/* Custom animations */
.animate-float {
	animation: float 6s ease-in-out infinite;
}

.animate-pulse-slow {
	animation: pulse-slow 4s ease-in-out infinite;
}

.animate-spin-slow {
	animation: spin-slow 60s linear infinite;
}

.animate-bounce-gentle {
	animation: bounce-gentle 2s ease-in-out infinite;
}

.animate-gradient {
	background-size: 200% auto;
	animation: gradient 8s ease infinite;
}

.animate-draw path {
	stroke-dasharray: 0 100;
	animation: draw 1.5s ease-out forwards;
}

/* Animation keyframes */
@keyframes float {
	0%, 100% { transform: translateY(0); }
	50% { transform: translateY(-10px); }
}

@keyframes pulse-slow {
	0%, 100% { opacity: 0.1; }
	50% { opacity: 0.2; }
}

@keyframes spin-slow {
	from { transform: rotate(0deg); }
	to { transform: rotate(360deg); }
}

@keyframes bounce-gentle {
	0%, 100% { transform: translateY(-5%); }
	50% { transform: translateY(5%); }
}

@keyframes gradient {
	0% { background-position: 0% 50%; }
	50% { background-position: 100% 50%; }
	100% { background-position: 0% 50%; }
}

@keyframes draw {
	0% { stroke-dasharray: 0 100; }
	100% { stroke-dasharray: 100 0; }
}

/* Hover effects */
.hover-lift {
	transition: transform 0.3s ease;
}

.hover-lift:hover {
	transform: translateY(-4px);
}

.hover-scale {
	transition: transform 0.3s ease;
}

.hover-scale:hover {
	transform: scale(1.05);
}

/* Slide animations */
.slide-in-right {
  opacity: 0;
  transform: translateX(30px);
  animation: slideInRight 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  will-change: transform, opacity;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Optimize card transitions */
.group {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform, box-shadow;
}

.group:hover {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}