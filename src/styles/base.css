@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
	--primary-color: #003820;
	--primary-light: #004d2e;
	--accent-color: #00c853;
	--navbar-height: 4rem;
	--content-width: min(87rem, 100% - 2rem);
	--container-padding-sm: 1rem;
	--container-padding-md: 1.5rem;
	--container-padding-lg: 2rem;
}

/* Base styles */
html {
	font-family: 'Inter', system-ui, sans-serif;
	background: #fff;
	scroll-behavior: smooth;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	font-size: 16px;
}

@media (max-width: 767px) {
	html {
		font-size: 14px;
	}
}

body {
	margin: 0;
	padding: 0;
	min-height: 100vh;
	line-height: 1.5;
	overflow-x: hidden;
	background-color: white;
}

main {
	min-height: calc(100vh - var(--navbar-height));
	width: 100%;
	max-width: var(--content-width);
	margin: 0 auto;
	padding: 0 var(--container-padding-lg);
	position: relative;
}

@media (max-width: 767px) {
	main {
		padding: 0 var(--container-padding-md);
	}
}

@media (max-width: 479px) {
	main {
		padding: 0 var(--container-padding-sm);
	}
}

/* Animation support */
* {
	transform-style: preserve-3d;
	backface-visibility: hidden;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

[data-aos] {
	transform: translateZ(0);
	will-change: transform, opacity;
}

/* Utility classes */
.text-gradient {
	background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	background-clip: text;
}

.bg-gradient-primary {
	background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
}

/* Reset styles */
* {
	box-sizing: border-box;
	margin: 0;
	padding: 0;
}

img {
	max-width: 100%;
	height: auto;
	display: block;
}

a {
	color: inherit;
	text-decoration: none;
}

button {
	background: none;
	border: none;
	padding: 0;
	cursor: pointer;
	font: inherit;
}

h1, h2, h3, h4, h5, h6 {
	line-height: 1.2;
}

@layer base {
	#markdown {
	  @apply text-black dark:text-white;
	}
	#markdown h1 {
	  @apply text-4xl font-extrabold tracking-tight mt-0;
	}
  
	#markdown h2 {
	  @apply text-3xl font-extrabold tracking-tight mt-10;
	}
  
	#markdown h3 {
	  @apply text-2xl font-extrabold tracking-tight mt-10;
	}
  
	#markdown h4 {
	  @apply mt-4 text-gray-500 mb-4;
	}
  
	#markdown p {
	  @apply mt-4;
	}
  
	#markdown ul,
	#markdown ol {
	  @apply mt-2  list-inside;
	}
	#markdown ul {
	  @apply list-disc;
	}
	#markdown ol {
	  @apply list-decimal;
	}
	#markdown li {
	  @apply pt-2;
	}
  }