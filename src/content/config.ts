import { defineCollection, z } from 'astro:content';

const blogCollection = defineCollection({
	type: 'content',
	schema: z.object({
		itemId: z.string(),
		title: z.string(),
		datePublished: z.string(),
		dateUpdated: z.string().optional(),
		metaImage: z.string(),
		author: z.string(),
		metaAuthor: z.string(),
		previousPost: z.string().optional(),
		previousPostTitle: z.string().optional(),
		nextPost: z.string().optional(),
		nextPostTitle: z.string().optional(),
		link: z.string().optional(),
		summary: z.string()
	})
});

export const collections = {
	'blog': blogCollection
};

