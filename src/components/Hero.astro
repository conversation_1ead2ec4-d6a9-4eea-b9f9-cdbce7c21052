---
import '@styles/animations.css';
---

<div class="relative w-full overflow-hidden">
	<!-- Enhanced Background Effects -->
	<div class="absolute inset-0 bg-gradient-to-br from-[#003820]/5 via-transparent to-[#003820]/10"></div>
	<div class="absolute inset-0 bg-[url('/grid-pattern.svg')] opacity-[0.02] animate-float-slow"></div>

	<!-- Floating Elements -->
	<div class="absolute top-20 left-10 w-32 h-32 bg-[#003820]/5 rounded-full blur-xl animate-float-1"></div>
	<div class="absolute top-40 right-20 w-24 h-24 bg-[#005732]/5 rounded-full blur-lg animate-float-2"></div>
	<div class="absolute bottom-20 left-1/4 w-40 h-40 bg-[#003820]/3 rounded-full blur-2xl animate-float-3"></div>

	<div class="relative max-w-[87rem] w-full mx-auto hero-container mt-4 md:mt-10 px-4 md:px-6">
		<div class="relative bg-white/90 backdrop-blur-xl border border-[#003820]/15 rounded-[2rem] overflow-hidden shadow-2xl">
			<!-- Enhanced Animated background patterns -->
			<div class="absolute inset-0 bg-gradient-to-br from-[#003820]/8 via-transparent to-[#005732]/8 animate-pulse-slow"></div>
			<div class="absolute -inset-[100%] bg-[radial-gradient(circle_at_30%_80%,rgba(0,56,32,0.15),transparent_50%)] animate-spin-slow"></div>
			<div class="absolute -inset-[100%] bg-[radial-gradient(circle_at_70%_20%,rgba(0,87,50,0.1),transparent_40%)] animate-spin-reverse"></div>

			<!-- Decorative Elements -->
			<div class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-[#003820]/30 to-transparent"></div>
			<div class="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-[#005732]/30 to-transparent"></div>

			<div class="relative pt-16 md:pt-28 px-6 md:px-12 pb-20 md:pb-36">
				<div class="max-w-[60rem] mx-auto text-center">
					<!-- Enhanced Premium badge with better animation -->
					<div class="inline-flex items-center bg-gradient-to-r from-[#003820]/10 to-[#005732]/10 border border-[#003820]/25 px-8 py-4 rounded-full mb-10 transform hover:scale-105 transition-all duration-700 shadow-xl hover:shadow-2xl backdrop-blur-sm animate-fade-in-up">
						<div class="relative w-7 h-7 mr-4">
							<div class="absolute inset-0 bg-[#003820] rounded-full animate-ping opacity-25"></div>
							<div class="absolute inset-0 bg-gradient-to-r from-[#003820] to-[#005732] rounded-full animate-pulse-gentle"></div>
							<svg class="relative z-10 text-white animate-bounce-gentle w-4 h-4" viewBox="0 0 24 24" fill="currentColor">
								<path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
							</svg>
						</div>
						<span class="text-[#003820] font-bold tracking-wide text-lg">Best Litigation Funding Platform</span>
						<div class="ml-3 px-3 py-1 bg-[#003820]/10 rounded-full">
							<span class="text-xs font-semibold text-[#003820]">2024</span>
						</div>
					</div>

				<!-- Enhanced Main headline with better typography and animations -->
				<h1 class="text-5xl md:text-7xl lg:text-8xl font-bold mb-10 relative leading-tight animate-fade-in-up delay-200">
					<span class="relative inline-block">
						<span class="animate-gradient bg-[length:300%_auto] bg-clip-text text-transparent bg-gradient-to-r from-[#003820] via-[#005732] via-[#003820] to-[#005732]">
							Are You Ready to
						</span>
						<div class="absolute -top-2 -right-2 w-4 h-4 bg-[#003820] rounded-full animate-pulse-gentle opacity-60"></div>
					</span>
					<br class="hidden md:block" />
					<span class="relative mt-2 inline-block">
						<span class="relative z-10 animate-gradient bg-[length:300%_auto] bg-clip-text text-transparent bg-gradient-to-r from-[#005732] via-[#003820] to-[#005732]">
							Unlock Exceptional
						</span>
						<div class="absolute -bottom-4 left-0 w-full h-3 overflow-hidden">
							<svg class="w-full h-full text-[#003820]/30 animate-draw-enhanced" preserveAspectRatio="none" viewBox="0 0 100 100">
								<path d="M0,60 Q25,20 50,60 T100,60" fill="none" stroke="currentColor" stroke-width="6" vector-effect="non-scaling-stroke"/>
							</svg>
						</div>
					</span>
					<br class="hidden md:block" />
					<span class="relative inline-block mt-2">
						<span class="animate-gradient bg-[length:300%_auto] bg-clip-text text-transparent bg-gradient-to-r from-[#003820] via-[#005732] to-[#003820]">
							Returns?
						</span>
						<div class="absolute -bottom-2 -right-3 w-6 h-6">
							<div class="w-full h-full bg-[#005732] rounded-full animate-bounce-gentle opacity-70"></div>
						</div>
					</span>
				</h1>

				<!-- Enhanced Subheadline with better spacing and animation -->
				<div class="max-w-[45rem] mx-auto mb-14 animate-fade-in-up delay-400">
					<p class="text-xl md:text-2xl text-[#3C4145]/85 leading-relaxed mb-4">
						Experience the future of litigation funding. Our platform connects co-funders with premium opportunities, delivering exceptional returns.
					</p>
					<p class="text-lg md:text-xl text-[#3C4145]/70 leading-relaxed">
						Join our exclusive community of investors and unlock access to high-value legal claims with proven success rates.
					</p>
				</div>

				<!-- Enhanced CTA Buttons with better design and animations -->
				<div class="flex flex-col sm:flex-row items-center justify-center gap-6 mb-20 animate-fade-in-up delay-600">
					<a href="https://app.3payglobal.com/#/signin" target="_blank" class="group relative inline-flex items-center justify-center px-10 py-5 text-lg font-bold text-white bg-gradient-to-r from-[#003820] to-[#005732] rounded-2xl overflow-hidden hover:shadow-2xl transition-all duration-700 transform hover:-translate-y-2 hover:scale-105 min-w-[200px]">
						<span class="relative z-10 flex items-center">
							<svg class="w-6 h-6 mr-3 animate-pulse-gentle" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
							</svg>
							Start Funding Now
							<svg class="w-5 h-5 ml-3 transform group-hover:translate-x-2 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3"/>
							</svg>
						</span>
						<div class="absolute inset-0 bg-gradient-to-r from-[#005732] to-[#003820] opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
						<div class="absolute -inset-1 bg-gradient-to-r from-[#003820] to-[#005732] rounded-2xl blur opacity-30 group-hover:opacity-60 transition-opacity duration-500"></div>
					</a>

					<a href="/about" class="group relative inline-flex items-center justify-center px-10 py-5 text-lg font-bold text-[#003820] bg-white border-2 border-[#003820] rounded-2xl hover:bg-[#003820] hover:text-white transition-all duration-500 transform hover:-translate-y-2 hover:scale-105 shadow-lg hover:shadow-xl min-w-[200px]">
						<svg class="w-5 h-5 mr-3 transform group-hover:rotate-12 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
						</svg>
						Learn More
						<svg class="w-5 h-5 ml-3 transform group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
						</svg>
					</a>
				</div>

				<!-- Enhanced Trust Indicators with better design and animations -->
				<div class="relative">
					<div class="absolute inset-0 bg-gradient-to-r from-transparent via-[#003820]/5 to-transparent rounded-2xl"></div>
					<div class="relative grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto p-8 animate-fade-in-up delay-800">
						<div class="text-center group">
							<div class="relative inline-block mb-4">
								<div class="absolute inset-0 bg-[#003820]/10 rounded-full blur-lg group-hover:blur-xl transition-all duration-500"></div>
								<div class="relative bg-white/80 backdrop-blur-sm border border-[#003820]/20 rounded-full p-6 group-hover:scale-110 transition-transform duration-500">
									<div class="text-4xl md:text-5xl font-bold text-[#003820] mb-2 animate-counter" data-target="50">£50M+</div>
								</div>
							</div>
							<div class="text-[#3C4145]/70 font-semibold">Claims Funded</div>
							<div class="text-sm text-[#3C4145]/50 mt-1">Total portfolio value</div>
						</div>
						<div class="text-center group">
							<div class="relative inline-block mb-4">
								<div class="absolute inset-0 bg-[#005732]/10 rounded-full blur-lg group-hover:blur-xl transition-all duration-500"></div>
								<div class="relative bg-white/80 backdrop-blur-sm border border-[#005732]/20 rounded-full p-6 group-hover:scale-110 transition-transform duration-500">
									<div class="text-4xl md:text-5xl font-bold text-[#005732] mb-2 animate-counter" data-target="100">100%</div>
								</div>
							</div>
							<div class="text-[#3C4145]/70 font-semibold">Success Rate</div>
							<div class="text-sm text-[#3C4145]/50 mt-1">All funded claims won</div>
						</div>
						<div class="text-center group">
							<div class="relative inline-block mb-4">
								<div class="absolute inset-0 bg-[#003820]/10 rounded-full blur-lg group-hover:blur-xl transition-all duration-500"></div>
								<div class="relative bg-white/80 backdrop-blur-sm border border-[#003820]/20 rounded-full p-6 group-hover:scale-110 transition-transform duration-500">
									<div class="text-4xl md:text-5xl font-bold text-[#003820] mb-2 animate-counter" data-target="75">75%</div>
								</div>
							</div>
							<div class="text-[#3C4145]/70 font-semibold">Max Returns</div>
							<div class="text-sm text-[#3C4145]/50 mt-1">Potential annual returns</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<style>
	/* Enhanced Floating Animations */
	@keyframes float-slow {
		0%, 100% { transform: translateY(0) rotate(0deg); }
		50% { transform: translateY(-15px) rotate(2deg); }
	}

	@keyframes float-1 {
		0%, 100% { transform: translateY(0) translateX(0) scale(1); }
		33% { transform: translateY(-20px) translateX(10px) scale(1.1); }
		66% { transform: translateY(10px) translateX(-5px) scale(0.9); }
	}

	@keyframes float-2 {
		0%, 100% { transform: translateY(0) translateX(0) scale(1); }
		50% { transform: translateY(-25px) translateX(-15px) scale(1.2); }
	}

	@keyframes float-3 {
		0%, 100% { transform: translateY(0) translateX(0) scale(1); }
		25% { transform: translateY(-10px) translateX(20px) scale(1.05); }
		75% { transform: translateY(15px) translateX(-10px) scale(0.95); }
	}

	.animate-float-slow { animation: float-slow 8s ease-in-out infinite; }
	.animate-float-1 { animation: float-1 12s ease-in-out infinite; }
	.animate-float-2 { animation: float-2 10s ease-in-out infinite; }
	.animate-float-3 { animation: float-3 14s ease-in-out infinite; }

	/* Enhanced Fade In Up Animation */
	@keyframes fade-in-up {
		0% {
			opacity: 0;
			transform: translateY(30px) scale(0.95);
		}
		100% {
			opacity: 1;
			transform: translateY(0) scale(1);
		}
	}

	.animate-fade-in-up {
		animation: fade-in-up 0.8s ease-out forwards;
		opacity: 0;
	}

	/* Animation Delays */
	.delay-200 { animation-delay: 0.2s; }
	.delay-400 { animation-delay: 0.4s; }
	.delay-600 { animation-delay: 0.6s; }
	.delay-800 { animation-delay: 0.8s; }

	/* Enhanced Ping Animation */
	@keyframes ping {
		75%, 100% {
			transform: scale(2.5);
			opacity: 0;
		}
	}

	.animate-ping {
		animation: ping 3s cubic-bezier(0, 0, 0.2, 1) infinite;
	}

	/* Enhanced Pulse Animation */
	@keyframes pulse-slow {
		0%, 100% { opacity: 0.1; }
		50% { opacity: 0.3; }
	}

	@keyframes pulse-gentle {
		0%, 100% { opacity: 0.7; transform: scale(1); }
		50% { opacity: 1; transform: scale(1.05); }
	}

	.animate-pulse-slow {
		animation: pulse-slow 6s ease-in-out infinite;
	}

	.animate-pulse-gentle {
		animation: pulse-gentle 3s ease-in-out infinite;
	}

	/* Enhanced Spin Animations */
	@keyframes spin-slow {
		from { transform: rotate(0deg); }
		to { transform: rotate(360deg); }
	}

	@keyframes spin-reverse {
		from { transform: rotate(360deg); }
		to { transform: rotate(0deg); }
	}

	.animate-spin-slow {
		animation: spin-slow 80s linear infinite;
	}

	.animate-spin-reverse {
		animation: spin-reverse 100s linear infinite;
	}

	/* Enhanced Bounce Animation */
	@keyframes bounce-gentle {
		0%, 100% {
			transform: translateY(-8%);
			animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
		}
		50% {
			transform: translateY(8%);
			animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
		}
	}

	.animate-bounce-gentle {
		animation: bounce-gentle 3s ease-in-out infinite;
	}

	/* Enhanced Gradient Animation */
	@keyframes gradient {
		0% { background-position: 0% 50%; }
		25% { background-position: 100% 50%; }
		50% { background-position: 100% 100%; }
		75% { background-position: 0% 100%; }
		100% { background-position: 0% 50%; }
	}

	.animate-gradient {
		animation: gradient 12s ease infinite;
	}

	/* Enhanced Draw Animation */
	@keyframes draw-enhanced {
		0% {
			stroke-dasharray: 0 200;
			stroke-dashoffset: 0;
		}
		50% {
			stroke-dasharray: 100 200;
			stroke-dashoffset: -50;
		}
		100% {
			stroke-dasharray: 200 0;
			stroke-dashoffset: -100;
		}
	}

	.animate-draw-enhanced path {
		stroke-dasharray: 0 200;
		animation: draw-enhanced 2s ease-out forwards;
		animation-delay: 1s;
	}

	/* Counter Animation */
	@keyframes counter {
		from { transform: scale(0.8); opacity: 0; }
		to { transform: scale(1); opacity: 1; }
	}

	.animate-counter {
		animation: counter 1s ease-out forwards;
	}

	/* Hover Effects */
	.group:hover .animate-counter {
		animation: counter 0.5s ease-out forwards;
	}

	/* Responsive Improvements */
	@media (max-width: 768px) {
		.animate-fade-in-up {
			animation-duration: 0.6s;
		}

		.animate-gradient {
			animation-duration: 8s;
		}
	}

	/* Performance Optimizations */
	.animate-float-slow,
	.animate-float-1,
	.animate-float-2,
	.animate-float-3,
	.animate-spin-slow,
	.animate-spin-reverse {
		will-change: transform;
	}

	.animate-gradient {
		will-change: background-position;
	}

	.animate-fade-in-up {
		will-change: opacity, transform;
	}
</style>
