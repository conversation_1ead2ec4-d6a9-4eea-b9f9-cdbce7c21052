---
import { Image } from 'astro:assets';
import '@styles/animations.css';
---


<div class="relative max-w-[87rem] w-full mx-auto hero-container mt-4 md:mt-10 px-4 md:px-6">
	<div class="relative bg-white/80 backdrop-blur-lg border border-[#003820]/10 rounded-[1.5rem] overflow-hidden">
		<!-- Animated background patterns -->
		<div class="absolute inset-0 bg-[url('/grid-pattern.svg')] opacity-5 animate-float"></div>
		<div class="absolute inset-0 bg-gradient-to-br from-[#003820]/5 via-transparent to-[#003820]/5 animate-pulse-slow"></div>
		<div class="absolute -inset-[100%] bg-[radial-gradient(circle_at_50%_120%,rgba(0,56,32,0.1),transparent_33%)] animate-spin-slow"></div>
		
		<div class="relative pt-12 md:pt-24 px-6 md:px-12 pb-16 md:pb-32">
			<div class="max-w-[57rem] mx-auto text-center">
				<!-- Premium badge -->
				<div class="inline-flex items-center bg-[#003820]/10 border border-[#003820]/20 px-6 py-3 rounded-full mb-8 transform hover:scale-105 transition-all duration-500 shadow-lg hover:shadow-xl">
					<div class="relative w-6 h-6 mr-3">
						<div class="absolute inset-0 bg-[#003820] rounded-full animate-ping opacity-20"></div>
						<div class="absolute inset-0 bg-[#003820]/20 rounded-full animate-pulse"></div>
						<svg class="relative z-10 text-[#003820] animate-bounce-gentle" viewBox="0 0 24 24" fill="currentColor">
							<path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10zm0-2a8 8 0 100-16 8 8 0 000 16zm-1-5h2v2h-2v-2zm0-8h2v6h-2V7z"/>
						</svg>
					</div>
					<span class="text-[#003820] font-semibold tracking-wide">Best Litigation Funding Platform</span>
				</div>

				<!-- Main headline with animated gradient -->
				<h1 class="text-5xl md:text-7xl lg:text-8xl font-bold mb-8 relative">
					<span class="animate-gradient bg-[length:200%_auto] bg-clip-text text-transparent bg-gradient-to-r from-[#003820] via-[#005732] to-[#003820]">
						Are You Ready to Unlock
					</span>
					<br class="hidden md:block" />
					<span class="relative mt-2 inline-block">
						 Exceptional Returns?
						<div class="absolute -bottom-3 left-0 w-full h-2 overflow-hidden">
							<svg class="w-full h-full text-[#003820]/20 animate-draw" preserveAspectRatio="none" viewBox="0 0 100 100">
								<path d="M0,50 Q50,0 100,50" fill="none" stroke="currentColor" stroke-width="8" vector-effect="non-scaling-stroke"/>
							</svg>
						</div>
					</span>
				</h1>

				<!-- Subheadline -->
				<p class="text-xl md:text-2xl text-[#3C4145]/80 max-w-[41rem] mx-auto mb-12 leading-relaxed">
					Experience the future of litigation funding. Our platform connects co-funders with premium opportunities, delivering exceptional returns.
				</p>

				<!-- CTA Buttons -->
				<div class="flex flex-col md:flex-row items-center justify-center gap-6 mb-16">
					<a href="https://app.3payglobal.com/#/signin" target="_blank" class="group relative inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-white bg-[#003820] rounded-xl overflow-hidden hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-1">
						<span class="relative z-10 flex items-center">
							Start Funding Now
							<svg class="w-5 h-5 ml-2 transform group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3"/>
							</svg>
						</span>
						<div class="absolute inset-0 bg-gradient-to-r from-[#003820] to-[#005732] opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
					</a>
					
					<a href="/learn-more" class="group inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-[#003820] border-2 border-[#003820] rounded-xl hover:bg-[#003820] hover:text-white transition-all duration-300 transform hover:-translate-y-1">
						Learn More
						<svg class="w-5 h-5 ml-2 transform group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
						</svg>
					</a>
				</div>

				<!-- Trust Indicators -->
				<div class="grid grid-cols-3 gap-8 max-w-3xl mx-auto">
					<div class="text-center">
						<div class="text-3xl font-bold text-[#003820] mb-2">£50M+</div>
						<div class="text-[#3C4145]/60">Claims Funded</div>
					</div>
					<div class="text-center">
						<div class="text-3xl font-bold text-[#003820] mb-2">100%</div>
						<div class="text-[#3C4145]/60">Success Rate</div>
					</div>
					<div class="text-center">
						<div class="text-3xl font-bold text-[#003820] mb-2">75%</div>
						<div class="text-[#3C4145]/60">Max Returns</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<style>
	@keyframes float {
		0%, 100% { transform: translateY(0); }
		50% { transform: translateY(-10px); }
	}

	.animate-float {
		animation: float 6s ease-in-out infinite;
	}

	@keyframes ping {
		75%, 100% { transform: scale(2); opacity: 0; }
	}

	.animate-ping {
		animation: ping 2s cubic-bezier(0, 0, 0.2, 1) infinite;
	}

	@keyframes pulse-slow {
		0%, 100% { opacity: 0.1; }
		50% { opacity: 0.2; }
	}

	.animate-pulse-slow {
		animation: pulse-slow 4s ease-in-out infinite;
	}

	@keyframes spin-slow {
		from { transform: rotate(0deg); }
		to { transform: rotate(360deg); }
	}

	.animate-spin-slow {
		animation: spin-slow 60s linear infinite;
	}

	@keyframes bounce-gentle {
		0%, 100% { transform: translateY(-5%); }
		50% { transform: translateY(5%); }
	}

	.animate-bounce-gentle {
		animation: bounce-gentle 2s ease-in-out infinite;
	}

	@keyframes gradient {
		0% { background-position: 0% 50%; }
		50% { background-position: 100% 50%; }
		100% { background-position: 0% 50%; }
	}

	.animate-gradient {
		animation: gradient 8s ease infinite;
	}

	@keyframes draw {
		0% { stroke-dasharray: 0 100; }
		100% { stroke-dasharray: 100 0; }
	}

	.animate-draw path {
		stroke-dasharray: 0 100;
		animation: draw 1.5s ease-out forwards;
	}
</style>
