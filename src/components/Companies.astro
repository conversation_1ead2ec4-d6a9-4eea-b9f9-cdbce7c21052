---
import { Image } from 'astro:assets';
import logo1 from '@src/assets/66eaef4fdae1e31ac290aef0_Logo (9).svg';
import logo2 from '@src/assets/66ea9caa60d0ba047f2de0c2_Logo (6).svg';
import logo3 from '@src/assets/66ea9caa6bb7795aef3034be_Logo (7).svg';
import logo4 from '@src/assets/66ea9caa21f40e1b433fcdd2_Logo (8).svg';
---

<div class="text-[#060B13] gap-x-12 flex flex-col flex-nowrap text-[0.88rem] tracking-[-0.005em] leading-[1.3] gap-y-12 py-12">
  <div class="text-[#060B13] tracking-[-0.005em] leading-[1.3]">
    <div class="text-[#060B13] text-3xl tracking-[-0.06563em] leading-[1.2] opacity-100 text-center fade-in">
      Trusted by 2k+ clients
    </div>
  </div>
  <div class="w-full overflow-hidden relative">
    <div class="logos">
      <div class="logos-slide">
        <Image src={logo1} alt="Company logo" class="h-10 mr-[6.25rem]" />
        <Image src={logo2} alt="Company logo" class="h-10 mr-[6.25rem]" />
        <Image src={logo3} alt="Company logo" class="h-10 mr-[6.25rem]" />
        <Image src={logo4} alt="Company logo" class="h-10 mr-[6.25rem]" />
        <Image src={logo4} alt="Company logo" class="h-10 mr-[6.25rem]" />
      </div>
    </div>
    <div class="bg-[linear-gradient(90deg,#FFFFFF,rgba(255,255,255,0))] absolute w-[9.375rem] z-[3] left-0 right-auto inset-y-0"></div>
    <div class="bg-[linear-gradient(-90deg,#FFFFFF,rgba(255,255,255,0))] absolute w-[9.375rem] z-[3] left-auto right-0 inset-y-0"></div>
  </div>
</div>

<style>
  .logos {
    overflow: hidden;
    padding: 20px 0;
    white-space: nowrap;
    position: relative;
  }

  .logos-slide {
    display: inline-flex;
    animation: slide 35s linear infinite;
  }

  @keyframes slide {
    from {
      transform: translateX(0);
    }
    to {
      transform: translateX(-100%);
    }
  }

  .logos:hover .logos-slide {
    animation-play-state: paused;
  }

  @media (max-width: var(--breakpoint-lg)) {
    .companies-container {
      gap: 2rem;
      padding: 0 var(--container-padding-lg);
    }
  }

  @media (max-width: var(--breakpoint-md)) {
    .companies-container {
      gap: 1.5rem;
      padding: 0 var(--container-padding-md);
    }
    .company-logo {
      height: 2rem;
    }
    .logos-slide {
      animation-duration: 25s;
    }
    .mr-[6.25rem] { margin-right: 3rem; }
    .w-[9.375rem] { width: 4rem; }
  }

  @media (max-width: var(--breakpoint-sm)) {
    .companies-container {
      gap: 1.5rem;
      padding: 0 var(--container-padding-sm);
      flex-wrap: wrap;
      justify-content: center;
    }
    .company-logo {
      height: 1.75rem;
    }
    .logos-slide {
      animation-duration: 20s;
    }
    .mr-[6.25rem] { margin-right: 2rem; }
  }
</style>


