---
import { Image } from 'astro:assets';
import '../styles/animations.css';

const features = [
	{
		icon: "/images/66f29ad666d331bb3b7b0f72_chart.svg",
		title: "Exceptional Returns",
		description: "Break free from mediocre returns with our proven system designed for success."
	},
	{
		icon: "/images/66f29ad5e454962d144c3811_bank.svg",
		title: "Dynamic Asset Class",
		description: "Our litigation funding significantly outperforms traditional savings options."
	},
	{
		icon: "/images/66ecaa037c28588af9bd00f9_tick-circle.svg",
		title: "Proven Track Record", 
		description: "100% success rate over the past 7 years with 5-75% return on successful claims."
	},
	{
		icon: "/images/66f29ad5422894011d63fa84_command.svg",
		title: "Elite Co-Funding",
		description: "Join a select group transforming how wealth grows through litigation funding."
	}
];
---

<div class="relative w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 boost-section overflow-hidden">
    <div class="absolute inset-0 pointer-events-none">
        <div class="absolute top-0 left-1/4 w-72 h-72 bg-[#003820]/5 rounded-full blur-3xl animate-pulse"></div>
        <div class="absolute bottom-0 right-1/4 w-96 h-96 bg-[#003820]/3 rounded-full blur-3xl animate-pulse" style="animation-delay: 1s"></div>
    </div>

    <div class="grid lg:grid-cols-2 gap-12 md:gap-16 relative">
        <div class="space-y-10 md:space-y-14">
            <div class="space-y-6 fade-in">
                <div>
                    <div class="inline-flex items-center bg-gradient-to-r from-[#003820]/10 to-[#003820]/5 backdrop-blur-sm px-6 py-3 rounded-full hover:scale-105 transition-all duration-300 hover:shadow-lg">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" class="w-5 h-5 mr-3 animate-bounce text-[#003820]">
                            <path d="M21 6l-3 9L12 3 6 15l-3-9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M3 6h18M3 18h18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        <span class="text-base font-medium text-[#003820]">High Returns</span>
                    </div>
                </div>
                <h2 class="text-5xl md:text-6xl lg:text-7xl font-normal tracking-tight leading-tight">
                    Put Your Money to Work <span class="text-[#003820] relative inline-block">Like Never Before</span>
                </h2>
            </div>
            
            <div class="grid sm:grid-cols-2 gap-8">
                {features.map((feature, index) => (
                    <div class="group flex flex-col space-y-5 p-8 rounded-3xl bg-white/30 backdrop-blur-sm hover:bg-white/50 hover:shadow-xl transition-all duration-500 hover:-translate-y-2" style={`animation-delay: ${index * 150}ms`}>
                        <div class="bg-gradient-to-br from-[#003820] to-[#243255] text-white flex items-center justify-center w-12 h-12 rounded-2xl transform transition-all duration-300 group-hover:scale-110 group-hover:rotate-6">
                            <Image src={feature.icon} width={24} height={24} alt={feature.title} class="w-6 h-6" />
                        </div>
                        <div class="space-y-3">
                            <h3 class="text-2xl font-normal text-[#003820] tracking-tight">{feature.title}</h3>
                            <p class="text-[#3C4145]/90 text-base leading-relaxed">{feature.description}</p>
                        </div>
                    </div>
                ))}
            </div>
        </div>

<div class="flex justify-center items-center lg:pt-16">
	<div class="relative w-full max-w-lg animate-float">
		<svg viewBox="0 0 400 400" class="w-full h-auto filter drop-shadow-2xl">
			<defs>
				<linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
					<stop offset="0%" style="stop-color:#003820;stop-opacity:1" />
					<stop offset="100%" style="stop-color:#243255;stop-opacity:1" />
				</linearGradient>
				<linearGradient id="grad2" x1="0%" y1="100%" x2="0%" y2="0%">
					<stop offset="0%" style="stop-color:#003820;stop-opacity:0.1" />
					<stop offset="100%" style="stop-color:#003820;stop-opacity:0" />
				</linearGradient>
				<filter id="glow">
					<feGaussianBlur stdDeviation="2" result="coloredBlur"/>
					<feMerge>
						<feMergeNode in="coloredBlur"/>
						<feMergeNode in="SourceGraphic"/>
					</feMerge>
				</filter>
			</defs>
			
			<!-- Background Elements -->
			<circle cx="200" cy="200" r="180" fill="url(#grad1)" opacity="0.05"/>
			<circle cx="200" cy="200" r="150" fill="url(#grad1)" opacity="0.08"/>
			
			<!-- Grid Lines -->
			<g class="grid-lines" stroke="#003820" stroke-opacity="0.1">
				{Array.from({length: 8}).map((_, i) => (
					<line 
						x1="50" 
						y1={100 + i * 30} 
						x2="350" 
						y2={100 + i * 30} 
						stroke-dasharray="4 4"
						class="grid-line"
						style={`--i: ${i}`}
					/>
				))}
			</g>
			
			<!-- Area under the curve -->
			<path 
				d="M 50,300 C 100,280 150,200 200,150 S 300,100 350,50 L 350,300 Z" 
				fill="url(#grad2)"
				class="area-path"
			/>
			
			<!-- Main Graph -->
			<path 
				d="M 50,300 C 100,280 150,200 200,150 S 300,100 350,50" 
				fill="none" 
				stroke="url(#grad1)" 
				stroke-width="3"
				filter="url(#glow)"
				class="graph-path"
			/>
			
			<!-- Data Points -->
			<g class="data-points">
				{[
					{ cx: 50, cy: 300, delay: 0, value: "0%" },
					{ cx: 125, cy: 220, delay: 0.2, value: "25%" },
					{ cx: 200, cy: 150, delay: 0.4, value: "50%" },
					{ cx: 275, cy: 100, delay: 0.6, value: "75%" },
					{ cx: 350, cy: 50, delay: 0.8, value: "100%" }
				].map((point) => (
					<g class="point-group">
						<circle 
							cx={point.cx} 
							cy={point.cy} 
							r="12" 
							class="point-halo"
							style={`animation-delay: ${point.delay}s`}
						/>
						<circle 
							cx={point.cx} 
							cy={point.cy} 
							r="6" 
							class="data-point" 
							style={`animation-delay: ${point.delay}s`}
						/>
						<g class="tooltip" opacity="0">
							<rect
								x={point.cx - 20}
								y={point.cy - 30}
								width="40"
								height="20"
								rx="4"
								fill="#003820"
							/>
							<text
								x={point.cx}
								y={point.cy - 16}
								text-anchor="middle"
								fill="white"
								font-size="12"
							>{point.value}</text>
						</g>
					</g>
				))}
			</g>
			
			<!-- Animated Highlight -->
			<circle cx="200" cy="150" r="8" fill="#003820" class="highlight-point"/>
			
			<!-- Value Indicators -->
			<g class="value-indicators" fill="#003820" font-size="12">
				<text x="40" y="305" text-anchor="end">0%</text>
				<text x="40" y="205" text-anchor="end">50%</text>
				<text x="40" y="105" text-anchor="end">100%</text>
			</g>
		</svg>
	</div>
</div>

<style>
	.graph-path {
		stroke-dasharray: 1000;
		stroke-dashoffset: 1000;
		animation: drawPath 2s ease-out forwards;
	}
	
	.area-path {
		opacity: 0;
		animation: fadeIn 1s ease-out 1s forwards;
	}
	
	.grid-line {
		opacity: 0;
		animation: fadeIn 0.5s ease-out forwards;
	}
	
	.data-point {
		fill: #003820;
		opacity: 0;
		transform-origin: center;
		animation: popIn 0.4s ease-out forwards;
		transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	}

	.point-halo {
		fill: #003820;
		opacity: 0;
		transform-origin: center;
		animation: haloEffect 3s ease-in-out infinite;
	}

	.point-group {
		cursor: pointer;
		transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	}

	.point-group:hover {
		transform: translateY(-4px);
	}

	.point-group:hover .data-point {
		transform: scale(1.2);
		filter: drop-shadow(0 4px 8px rgba(0,56,32,0.3));
	}

	.point-group .tooltip {
		transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
		transform: translateY(10px);
		opacity: 0;
	}
	
	.point-group:hover .tooltip {
		opacity: 1;
		transform: translateY(0);
	}

	@keyframes haloEffect {
		0%, 100% {
			opacity: 0;
			transform: scale(0.8);
		}
		50% {
			opacity: 0.15;
			transform: scale(1.4);
		}
	}
	
	.highlight-point {
		animation: pulse 2s ease-in-out infinite;
		filter: drop-shadow(0 0 12px rgba(0,56,32,0.4));
	}
	
	.value-indicators text {
		opacity: 0;
		animation: fadeIn 0.5s ease-out forwards;
	}
	
	@keyframes drawPath {
		to {
			stroke-dashoffset: 0;
		}
	}
	
	@keyframes fadeIn {
		to {
			opacity: 1;
		}
	}
	
	@keyframes popIn {
		from {
			opacity: 0;
			transform: scale(0);
		}
		50% {
			transform: scale(1.2);
		}
		to {
			opacity: 1;
			transform: scale(1);
		}
	}
	
	@keyframes pulse {
		0%, 100% {
			transform: scale(1);
			opacity: 0.8;
		}
		50% {
			transform: scale(1.5);
			opacity: 0.4;
		}
	}
	
	.animate-float {
		animation: float 6s ease-in-out infinite;
	}
	
	@keyframes float {
		0%, 100% { transform: translateY(0); }
		50% { transform: translateY(-20px); }
	}
</style>




