---
interface Props {
	class?: string;
}

const { class: className } = Astro.props;
import '../styles/animations.css';
---

<section class:list={["w-full why-fund-section", className]}>
	<div class="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 md:py-20 lg:py-24">
		<div class="relative">
			<!-- Background decorative elements -->
			<div class="absolute inset-0 pointer-events-none">
				<div class="absolute top-0 left-1/4 w-72 h-72 bg-[#003820]/5 rounded-full blur-3xl animate-pulse"></div>
				<div class="absolute bottom-0 right-1/4 w-96 h-96 bg-[#003820]/3 rounded-full blur-3xl animate-pulse" style="animation-delay: 1s"></div>
			</div>

			<div class="relative z-10 space-y-12">
				<!-- Header -->
				<div class="text-center space-y-6 fade-in">
					<div class="inline-flex items-center bg-gradient-to-r from-[#003820]/10 to-[#003820]/5 backdrop-blur-sm px-6 py-3 rounded-full hover:scale-105 transition-all duration-300 hover:shadow-lg">
						<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" class="w-5 h-5 mr-3 text-[#003820]">
							<path d="M12 2L2 7l10 5 10-5-10-5z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
							<path d="M2 17l10 5 10-5M2 12l10 5 10-5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
						</svg>
						<span class="text-base font-medium text-[#003820]">Investment Opportunity</span>
					</div>
					<h2 class="text-4xl md:text-5xl lg:text-6xl font-normal text-[#003820] tracking-tight leading-tight">
						Why Fund with <span class="text-[#184E35] relative inline-block">3Pay?</span>
					</h2>
				</div>

				<!-- Main content -->
				<div class="max-w-4xl mx-auto space-y-8">
					<div class="text-center space-y-6 slide-in-up">
						<p class="text-xl md:text-2xl text-[#3C4145] leading-relaxed">
							Imagine your capital not just returned, but <span class="text-[#003820] font-semibold">significantly grown</span>. That's the opportunity at the heart of litigation funding with 3Pay Global.
						</p>
					</div>

					<!-- Key benefits cards -->
					<div class="grid md:grid-cols-2 gap-8 mt-12">
						<div class="benefit-card group">
							<div class="card-border absolute inset-0 rounded-2xl -z-10"></div>
							<div class="relative z-10 p-8 bg-white/95 rounded-2xl transition-all duration-300">
								<div class="flex items-start space-x-4">
									<div class="bg-gradient-to-br from-[#003820] to-[#243255] text-white flex items-center justify-center w-12 h-12 rounded-2xl transform transition-all duration-300 group-hover:scale-110 group-hover:rotate-6 flex-shrink-0">
										<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" class="w-6 h-6">
											<path d="M12 2L2 7l10 5 10-5-10-5z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
											<path d="M2 17l10 5 10-5M2 12l10 5 10-5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
										</svg>
									</div>
									<div>
										<h3 class="text-xl font-semibold text-[#003820] mb-2">Exceptional Returns</h3>
										<p class="text-[#3C4145] leading-relaxed">
											When a claim succeeds, you recover your full investment plus a chosen and pre-agreed return from <span class="font-semibold text-[#003820]">5% and up to a maximum of 75%</span>.
										</p>
									</div>
								</div>
							</div>
						</div>

						<div class="benefit-card group">
							<div class="card-border absolute inset-0 rounded-2xl -z-10"></div>
							<div class="relative z-10 p-8 bg-white/95 rounded-2xl transition-all duration-300">
								<div class="flex items-start space-x-4">
									<div class="bg-gradient-to-br from-[#003820] to-[#243255] text-white flex items-center justify-center w-12 h-12 rounded-2xl transform transition-all duration-300 group-hover:scale-110 group-hover:rotate-6 flex-shrink-0">
										<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" class="w-6 h-6">
											<path d="M9 12l2 2 4-4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
											<circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
										</svg>
									</div>
									<div>
										<h3 class="text-xl font-semibold text-[#003820] mb-2">Expert-Backed Cases</h3>
										<p class="text-[#3C4145] leading-relaxed">
											Every case we fund is backed by <span class="font-semibold text-[#003820]">leading barristers and legal experts</span>, and each one is rigorously vetted before a single pound is committed.
										</p>
									</div>
								</div>
							</div>
						</div>
					</div>

					<!-- Call to action content -->
					<div class="text-center space-y-8 mt-16 slide-in-up">
						<p class="text-lg md:text-xl text-[#3C4145] leading-relaxed">
							Are you ready to see what your money can truly achieve? Let's talk today.
						</p>
					
						<div class="space-y-4">
							<!-- <p class="text-base text-[#3C4145]">
								Contact us now at <a href="mailto:<EMAIL>" class="text-[#003820] font-semibold hover:text-[#184E35] transition-colors duration-300 underline decoration-2 underline-offset-4"><EMAIL></a>
							</p> -->
							<p class="text-base text-[#3C4145]">
								Take the first step toward making your money work <span class="font-semibold text-[#003820]">harder and smarter</span>.
							</p>
						</div>
						<a href="mailto:<EMAIL>" class="inline-flex items-center justify-center px-8 py-4 text-base text-white bg-gradient-to-r from-[#184E35] to-[#243255] rounded-full hover:opacity-90 hover:shadow-lg transition-all duration-300 scale-in">
							<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" class="w-5 h-5 mr-2">
								<path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z" stroke="currentColor" stroke-width="2"/>
								<polyline points="22,6 12,13 2,6" stroke="currentColor" stroke-width="2"/>
							</svg>
							Contact Us Today
						</a>
					</div>
				</div>
			</div>
		</div>
	</div>
</section>

<style>
	.benefit-card {
		border: 4px solid transparent;
		background-clip: padding-box;
		position: relative;
		isolation: isolate;
		transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
		border-radius: 1rem;
	}

	.card-border {
		background: linear-gradient(115deg, 
			#003820 0%,
			#184E35 25%,
			#243255 50%,
			#184E35 75%,
			#003820 100%
		);
		background-size: 300% 300%;
		animation: gradient-shift 8s ease infinite;
		opacity: 0.15;
		transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
		filter: blur(12px);
		transform: scale(1.05);
	}

	.benefit-card:hover {
		transform: translateY(-6px);
		box-shadow: 0 20px 40px -10px rgba(0, 56, 32, 0.25);
	}

	.benefit-card:hover .card-border {
		opacity: 0.4;
		animation-duration: 4s;
		filter: blur(8px);
		transform: scale(1.02);
	}

	@keyframes gradient-shift {
		0% { background-position: 0% 50%; }
		50% { background-position: 100% 50%; }
		100% { background-position: 0% 50%; }
	}
</style>
