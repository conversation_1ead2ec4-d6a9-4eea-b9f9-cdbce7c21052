---
import { Image } from 'astro:assets';
import '../styles/animations.css';

interface Props {
  class?: string;
}

const { class: className } = Astro.props;

const workflowCards = [
  {
    icon: "/images/selective_funding.svg",
    title: "Selective Litigation Funding",
    description: "We finance high value legal claims through top tier solicitors, giving individuals the power to challenge well-funded opponents such as global corporations, insurance firms, solicitors, auditors, accountants, and failed property developers.",
    image: "/images/workflow1.avif",
    imageWidth: "57%",
    imagePadding: "72%"
  },
  {
    icon: "/images/case_assessment.svg",
    title: "How Do Claims Qualify?",
    description: `At 3Pay Global, excellence is non-negotiable. To ensure we only back the strongest cases, claims must meet strict criteria. The case must demonstrate strong merits, showing a high likelihood of success and must be managed by experienced litigation solicitors who specialise in the relevant area`,
    image: "/images/workflow2.avif",
    imageWidth: "79%",
    imagePadding: "71%"
  },
  {
    icon: "/images/smart_funding_approach.svg",
    title: "A Smarter Way to Fund",
    description: `This is not just capital. It is strategy. At 3Pay, we align your investment with a proven litigation model that consistently outperforms traditional savings and property returns. \n
     Opportunities like this are rare. But when they do arise, the rewards are transformational.`,
    image: "/images/workflow3.webp",
    imageWidth: "75%",
    imagePadding: "71%"
  },
  {
    icon: "/images/case_management.svg",
    title: "Capital With Purpose",
    description: "With 3Pay, your money does more than grow. It supports strong legal claims, drives meaningful outcomes, and delivers exceptional financial and ethical returns",
    image: "/images/workflow4.avif",
    imageWidth: "78%",
    imagePadding: "84%"
  }
];
---

<section class:list={["w-full workflow-section", className]}>
  <div class="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 md:py-16 lg:py-20">
  <div class="flex flex-col items-center space-y-8 md:space-y-12 lg:space-y-16">
    <div class="flex flex-col items-center space-y-4 max-w-3xl text-center">
      <div class="inline-flex items-center bg-[#003820]/5 shadow-[rgba(0,0,0,0.08)_0_0_1px_1px,rgba(255,255,255,0.25)_4px_4px_4px_inset] text-[#003820] px-3 py-2 rounded-full">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" class="w-4 h-4 mr-2">
          <path d="M3 6h18M3 12h18M3 18h18" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
          <circle cx="7" cy="6" r="2" fill="currentColor"/>
          <circle cx="12" cy="12" r="2" fill="currentColor"/>
          <circle cx="17" cy="18" r="2" fill="currentColor"/>
        </svg>
        <span class="text-sm">Our Process</span>
      </div>
      <h2 class="text-3xl md:text-4xl lg:text-6xl font-normal text-[#003820] tracking-tight leading-tight group animate-fade-in">
        How we guarantee higher <span class="text-[#184E35] relative after:content-[''] after:absolute after:-bottom-2 after:left-0 after:w-full after:h-1 after:bg-gradient-to-r after:from-[#184E35]/20 after:to-[#243255]/20 after:transform after:scale-x-0 after:transition-transform after:duration-300 group-hover:after:scale-x-100">returns</span>
      </h2>
    </div>
    
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 w-full">

      {workflowCards.map((card, index) => (
      <div class="group flex flex-col bg-gradient-to-br from-white to-[#003820]/5 rounded-3xl overflow-hidden hover:shadow-xl transition-all duration-500 slide-in-right hover:-translate-y-1" style={`animation-delay: ${index * 150}ms`}>
          <div class="p-6 md:p-8 relative z-10 bg-gradient-to-b from-transparent to-[#003820]/5">
          <div class="flex flex-col space-y-6">
          <div class="bg-gradient-to-r from-[#184E35] to-[#243255] text-white w-12 h-12 rounded-full flex items-center justify-center transform transition-all duration-500 group-hover:scale-110 group-hover:rotate-12 group-hover:shadow-[0_0_20px_rgba(0,56,32,0.3)]">
            <Image src={card.icon} width={24} height={24} alt={card.title} class="w-6 h-6 transition-transform duration-500 group-hover:scale-110" />
          </div>
          <div class="overflow-hidden">
            <h3 class="text-2xl md:text-3xl lg:text-4xl font-normal bg-gradient-to-r from-[#003820] to-[#184E35] bg-clip-text text-transparent transform transition-all duration-500 group-hover:translate-x-2">{card.title}</h3>
          </div>
          <p class="text-[#3C4145] text-sm md:text-base leading-relaxed transition-all duration-500 group-hover:text-[#003820] group-hover:pl-2">{card.description}</p>
          </div>
          </div>

          {/* <div class="relative w-full mt-6">
          <div class="aspect-[4/3] md:aspect-[16/9] relative">
            <Image 
            src={card.image} 
            width={500} 
            height={360} 
            alt={card.title} 
            class="w-full h-full object-cover transform transition-all duration-500 group-hover:scale-[1.02]" 
            />
            <div class="absolute inset-x-0 bottom-0 h-24 bg-gradient-to-b from-transparent to-[#003820]/5"></div>
          </div>
          </div> */}
      </div>
      ))}
    </div>
    </div>
  </div>
</section>




