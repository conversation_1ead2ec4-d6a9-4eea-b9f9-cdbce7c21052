---
---

<nav class="fixed top-0 left-0 right-0 z-50 px-4 py-4">
	<div class="max-w-7xl mx-auto">
		<div class="bg-white/80 backdrop-blur-lg border border-[#003820]/10 rounded-full px-6 py-3 flex items-center justify-between">
			<a href="/" class="flex items-center">
				<img src="/images/3paylogo.png" alt="3Pay Logo" class="h-8 w-auto" />
			</a>
			
			<div class="hidden md:flex items-center space-x-8">
				<a href="/solicitors" class="text-gray-700 hover:text-[#003820] transition-colors">Solicitors</a>
				<a href="/claimants" class="text-gray-700 hover:text-[#003820] transition-colors">Claimants</a>
				<a href="/co-funders" class="text-gray-700 hover:text-[#003820] transition-colors">Co-Funders</a>
				<a href="/blog" class="text-gray-700 hover:text-[#003820] transition-colors">Blog</a>
				<div class="relative">
					<button class="company-button text-[#3C4145] text-base font-normal hover:text-[#003820] transition-colors duration-200 flex items-center gap-2">
						Company
						<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
							<path d="m6 9 6 6 6-6"/>
						</svg>
					</button>
					<div id="company-submenu" class="absolute hidden top-[calc(100%+10px)] left-0 bg-white rounded-lg shadow-lg mt-2 py-2 w-48 transition-all duration-300">
						<!-- <a href="/podcast" class="block px-4 py-2 text-[#3C4145] hover:text-[#003820] hover:bg-[rgba(0,56,32,0.05)] transition-colors duration-200">Podcast</a> -->
						<a href="/about" class="block px-4 py-2 text-[#3C4145] hover:text-[#003820] hover:bg-[rgba(0,56,32,0.05)] transition-colors duration-200">About</a>
						<a href="/help" class="block px-4 py-2 text-[#3C4145] hover:text-[#003820] hover:bg-[rgba(0,56,32,0.05)] transition-colors duration-200">Help</a>
						<a href="/contact" class="block px-4 py-2 text-[#3C4145] hover:text-[#003820] hover:bg-[rgba(0,56,32,0.05)] transition-colors duration-200">Contact</a>
					</div>
				</div>
			</div>

			<div class="flex items-center gap-4">
				<a href="https://app.3payglobal.com/#/signin" target="_blank" class="bg-[#003820] text-white px-6 py-2 rounded-full hover:bg-[#004d2e] transition-colors">
					Apply Now
				</a>
				<button class="md:hidden flex flex-col items-center justify-center w-10 h-10 bg-[#003820] rounded border border-white/20" id="mobile-menu-button" aria-label="Toggle menu">
					<span class="w-5 h-[0.1rem] bg-white rounded-sm transition-all duration-300 nav-button_line is-first"></span>
					<span class="w-5 h-[0.1rem] bg-white rounded-sm transition-all duration-300 mt-1 nav-button_line is-second"></span>
					<span class="w-5 h-[0.1rem] bg-white rounded-sm transition-all duration-300 mt-1 nav-button_line is-third"></span>
				</button>
			</div>
		</div>
	</div>
</nav>

<div id="mobile-menu" class="fixed top-[4.5rem] left-4 right-4 bg-white/95 backdrop-blur-lg rounded-2xl shadow-lg py-6 w-auto md:hidden transition-all duration-300 z-[60] opacity-0 -translate-y-4 invisible border border-[#003820]/10">
	<div class="flex flex-col space-y-1">
		<a href="/solicitors" class="flex items-center px-6 py-3 text-[#3C4145] hover:text-[#003820] hover:bg-[#003820]/5 transition-all duration-200 rounded-lg">
			<span class="text-base font-medium">Solicitors</span>
		</a>
		<a href="/claimants" class="flex items-center px-6 py-3 text-[#3C4145] hover:text-[#003820] hover:bg-[#003820]/5 transition-all duration-200 rounded-lg">
			<span class="text-base font-medium">Claimants</span>
		</a>
		<a href="/co-funders" class="flex items-center px-6 py-3 text-[#3C4145] hover:text-[#003820] hover:bg-[#003820]/5 transition-all duration-200 rounded-lg">
			<span class="text-base font-medium">Co-Funders</span>
		</a>
		<a href="/blog" class="flex items-center px-6 py-3 text-[#3C4145] hover:text-[#003820] hover:bg-[#003820]/5 transition-all duration-200 rounded-lg">
			<span class="text-base font-medium">Blog</span>
		</a>
		
		<div class="h-px bg-[#003820]/10 my-2 mx-6"></div>
		
		<a href="/podcast" class="flex items-center px-6 py-3 text-[#3C4145] hover:text-[#003820] hover:bg-[#003820]/5 transition-all duration-200 rounded-lg">
			<span class="text-base font-medium">Podcast</span>
		</a>
		<a href="/about" class="flex items-center px-6 py-3 text-[#3C4145] hover:text-[#003820] hover:bg-[#003820]/5 transition-all duration-200 rounded-lg">
			<span class="text-base font-medium">About</span>
		</a>
		<a href="/help" class="flex items-center px-6 py-3 text-[#3C4145] hover:text-[#003820] hover:bg-[#003820]/5 transition-all duration-200 rounded-lg">
			<span class="text-base font-medium">Help</span>
		</a>
		<a href="/contact" class="flex items-center px-6 py-3 text-[#3C4145] hover:text-[#003820] hover:bg-[#003820]/5 transition-all duration-200 rounded-lg">
			<span class="text-base font-medium">Contact</span>
		</a>
	</div>
</div>

<style>
	.company-button {
		position: relative;
	}

	.nav-button_line {
		transform-origin: center;
		transition: transform 0.3s ease, opacity 0.3s ease;
	}

	.mobile-menu-open .is-first {
		transform: translateY(0.35rem) rotate(45deg);
	}

	.mobile-menu-open .is-second {
		opacity: 0;
	}

	.mobile-menu-open .is-third {
		transform: translateY(-0.35rem) rotate(-45deg);
	}
</style>


<script>
	const companyButton = document.querySelector('.company-button');
	const companySubmenu = document.getElementById('company-submenu');
	const mobileMenuButton = document.getElementById('mobile-menu-button');
	const mobileMenu = document.getElementById('mobile-menu');
	let isMenuOpen = false;

	companyButton?.addEventListener('click', () => {
		companySubmenu?.classList.toggle('hidden');
	});

	document.addEventListener('click', (event) => {
		if (!companyButton?.contains(event.target as Node) && !companySubmenu?.contains(event.target as Node)) {
			companySubmenu?.classList.add('hidden');
		}
	});

	const toggleMenu = () => {
		isMenuOpen = !isMenuOpen;
		mobileMenuButton?.classList.toggle('mobile-menu-open');
		
		if (isMenuOpen) {
			mobileMenu?.classList.remove('invisible');
			// Use setTimeout to trigger animation after visibility is set
			setTimeout(() => {
				mobileMenu?.classList.remove('opacity-0', '-translate-y-4');
			}, 10);
		} else {
			mobileMenu?.classList.add('opacity-0', '-translate-y-4');
			// Wait for animation to finish before hiding menu
			setTimeout(() => {
				mobileMenu?.classList.add('invisible');
			}, 300);
		}
	};

	mobileMenuButton?.addEventListener('click', toggleMenu);
</script>
