---
interface Props {
	class?: string;
}

const { class: className } = Astro.props;
import '../styles/animations.css';
---

<section class:list={["w-full cta-section", className]}>
	<div class="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 md:py-16 lg:py-20">
		<div class="flex flex-col items-center space-y-8 md:space-y-12">
			<div class="flex flex-col items-center space-y-4 max-w-3xl text-center fade-in">
				<div class="inline-flex items-center bg-[#003820]/5 shadow-[rgba(0,0,0,0.08)_0_0_1px_1px,rgba(255,255,255,0.25)_4px_4px_4px_inset] text-[#003820] px-3 py-2 rounded-full hover:scale-105 transition-transform duration-300">
					<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" class="w-4 h-4 mr-2">
						<path d="M12 3L3 8l9 5 9-5-9-5z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
						<path d="M3 13l9 5 9-5M3 18l9 5 9-5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
					</svg>
					<span class="text-sm">Start Today</span>
				</div>
				<!-- <h2 class="text-3xl md:text-5xl lg:text-6xl font-normal text-[#003820] tracking-tight leading-tight group">
					Don't Let Cost <span class="text-[#184E35] relative after:content-[''] after:absolute after:-bottom-2 after:left-0 after:w-full after:h-1 after:bg-gradient-to-r after:from-[#184E35]/20 after:to-[#243255]/20 after:transform after:scale-x-0 after:transition-transform after:duration-300 group-hover:after:scale-x-100">Hold You Back</span>
				</h2> -->
				
				<div class="grid grid-cols-1 md:grid-cols-2 gap-6 w-full mt-8">
					<div class="cta-card relative">
						<div class="card-border absolute inset-0 rounded-2xl -z-10"></div>
						<div class="flex flex-col gap-2 p-6 bg-white/95 rounded-2xl transition-all duration-300 slide-in-left relative z-10">
							<h3 class="text-xl font-semibold text-[#003820]">100% Success Rate</h3>
							<p class="text-[#3C4145] text-sm md:text-base">Over the past 7 years with returns of 5-75% on successful claims.</p>
						</div>
					</div>
					<div class="cta-card relative">
						<div class="card-border absolute inset-0 rounded-2xl -z-10"></div>
						<div class="flex flex-col gap-2 p-6 bg-white/95 rounded-2xl transition-all duration-300 slide-in-right relative z-10">
							<h3 class="text-xl font-semibold text-[#003820]">£3M Coverage</h3>
							<p class="text-[#3C4145] text-sm md:text-base">Full funding for legal costs with no upfront fees. You only pay if you win.</p>
						</div>
					</div>
				</div>
			</div>

			<a href="https://app.3payglobal.com/#/signin" target="_blank" class="inline-flex items-center justify-center px-8 py-4 text-base text-white bg-gradient-to-r from-[#184E35] to-[#243255] rounded-full hover:opacity-90 hover:shadow-lg transition-all duration-300 scale-in w-full md:w-auto">
				Apply Now
			</a>
		</div>
	</div>
</section>

<style>
	.cta-card {
		border: 4px solid transparent;
		background-clip: padding-box;
		position: relative;
		isolation: isolate;
		transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
		border-radius: 1rem;
	}

	.card-border {
		background: linear-gradient(115deg, 
			#003820 0%,
			#184E35 25%,
			#243255 50%,
			#184E35 75%,
			#003820 100%
		);
		background-size: 300% 300%;
		animation: gradient-shift 8s ease infinite;
		opacity: 0.15;
		transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
		filter: blur(12px);
		transform: scale(1.05);
	}

	.cta-card:hover {
		transform: translateY(-6px);
		box-shadow: 0 20px 40px -10px rgba(0, 56, 32, 0.25);
	}

	.cta-card:hover .card-border {
		opacity: 0.4;
		animation-duration: 4s;
		filter: blur(8px);
		transform: scale(1.02);
	}

	@keyframes gradient-shift {
		0% { background-position: 0% 50%; }
		50% { background-position: 100% 50%; }
		100% { background-position: 0% 50%; }
	}

</style>

