---
import { Image } from 'astro:assets';
import '../styles/animations.css';

const testimonials = [
	{
		name: "<PERSON>",
		avatar: "/images/66f2bed2d3cc7dd4fc33225b_Ellipse 13.svg",
		position: "CEO - Test Company",
		text: "The litigation funding from 3Pay Global was instrumental in our case success. Their efficient process and expert team helped us secure the necessary resources without any financial strain.",
		date: "August 29, 2024"
	},
	{
		name: "<PERSON>",
		avatar: "/images/66ffea2e20a842e078b275a2_Ellipse 13 (1).svg",
		position: "CEO - Test Company",
		text: "As a law firm handling complex cases, having 3Pay Global as our funding partner has been transformative. Their understanding of litigation needs and quick funding decisions make them invaluable.",
		date: "August 29, 2024"
	},
	{
		name: "<PERSON>",
		avatar: "/images/66ffea2a20740922b1680f60_Ellipse 13 (2).svg",
		position: "CEO - Test Company",
		text: "3Pay Global's litigation funding enabled us to pursue justice without compromise. Their support throughout the process and transparent terms made all the difference in our case outcome.",
		date: "August 29, 2024"
	}
];
---

<div class="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 sm:py-12 md:py-16 lg:py-20">
	<div class="grid grid-cols-1 lg:grid-cols-[1fr_1.25fr] gap-8 lg:gap-16">
		<!-- Left Column -->
		<div class="flex flex-col justify-between md:items-center lg:items-start">
			<div class="space-y-6 md:space-y-8">
				<div class="flex flex-col space-y-3 md:items-center lg:items-start">
					<div>
						<div class="inline-flex items-center bg-[#003820]/5 shadow-[rgba(0,0,0,0.08)_0_0_1px_1px,rgba(255,255,255,0.25)_4px_4px_4px_inset] text-[#003820] px-4 py-2 rounded-full hover:scale-105 transition-transform duration-300">
							<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" class="w-4 h-4 mr-2">
								<path d="M8 12H8.01M12 12H12.01M16 12H16.01M21 12C21 16.97 16.97 21 12 21C7.03 21 3 16.97 3 12C3 7.03 7.03 3 12 3C16.97 3 21 7.03 21 12Z" 
									stroke="currentColor" 
									stroke-width="2" 
									stroke-linecap="round" 
									stroke-linejoin="round"/>
							</svg>
							<span class="text-sm">Testimonials</span>
						</div>
					</div>
					<h2 class="text-4xl md:text-5xl lg:text-6xl font-normal tracking-tight leading-tight group animate-fade-in">
						What our clients are <span class="text-[#184E35] relative after:content-[''] after:absolute after:-bottom-2 after:left-0 after:w-full after:h-1 after:bg-gradient-to-r after:from-[#184E35]/20 after:to-[#243255]/20 after:transform after:scale-x-0 after:transition-transform after:duration-300 group-hover:after:scale-x-100">saying?</span>
					</h2>
				</div>
				<p class="text-[#3C4145] text-base md:text-center lg:text-left max-w-2xl mx-auto lg:mx-0">
					Our litigation funding platform is revolutionizing access to justice. Here's what our partners say about their experience with our funding solutions.
				</p>
			</div>
		</div>

		<!-- Right Column -->
		<div class="relative h-[28rem] sm:h-[34rem] overflow-hidden">
			<div class="absolute inset-0">
				<div class="testimonials-scroll">
					{[...testimonials, ...testimonials, ...testimonials].map((testimonial, index) => (
						<div class="testimonial-card bg-white/95 flex flex-col p-4 sm:p-6 rounded-3xl hover:shadow-xl transition-all duration-300 hover:-translate-y-1 relative">
							<div class="card-border absolute inset-0 rounded-3xl -z-10"></div>
							<div class="space-y-6">
								<div class="flex gap-6">
									<div class="h-16 w-16 rounded-full overflow-hidden flex-shrink-0 bg-[#003820]/10">
										<img src={testimonial.avatar} alt={testimonial.name} class="w-full h-full object-cover" />
									</div>
									<div class="space-y-2">
										<div class="text-xl font-medium text-[#003820]">{testimonial.name}</div>
										<div class="flex gap-2">
											<p class="text-sm text-[#3C4145]/80">{testimonial.position}</p>
										</div>
									</div>
								</div>
								<p class="text-[#3C4145] text-base leading-relaxed">{testimonial.text}</p>
								<div class="text-sm text-[#3C4145]/80">{testimonial.date}</div>
							</div>
						</div>
				))}
			</div>
			<div class="absolute inset-x-0 bottom-0 h-24 sm:h-28 bg-gradient-to-t from-white to-transparent pointer-events-none z-10"></div>
			<div class="absolute inset-x-0 top-0 h-24 sm:h-28 bg-gradient-to-b from-white to-transparent pointer-events-none z-10"></div>
		</div>
	</div>
</div>

<style>
	.testimonial-card {
		border: 4px solid transparent;
		background-clip: padding-box;
		position: relative;
		isolation: isolate;
		transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
	}

	.card-border {
		background: linear-gradient(115deg, 
			#003820 0%,
			#184E35 25%,
			#243255 50%,
			#184E35 75%,
			#003820 100%
		);
		background-size: 300% 300%;
		animation: gradient-shift 8s ease infinite;
		opacity: 0.15;
		transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
		filter: blur(12px);
		transform: scale(1.05);
	}

	.testimonial-card:hover {
		transform: translateY(-6px);
		box-shadow: 0 20px 40px -10px rgba(0, 56, 32, 0.25);
	}

	.testimonial-card:hover .card-border {
		opacity: 0.4;
		animation-duration: 4s;
		filter: blur(8px);
		transform: scale(1.02);
	}

	@keyframes gradient-shift {
		0% { background-position: 0% 50%; }
		50% { background-position: 100% 50%; }
		100% { background-position: 0% 50%; }
	}

	.testimonials-scroll {
		animation: scroll 240s linear infinite;
		will-change: transform;
		display: flex;
		flex-direction: column;
		gap: 1.5rem;
		padding: 0.5rem;
		transition: all 0.3s ease;
	}

	.testimonials-scroll:hover,
	.testimonials-scroll:focus {
		animation-play-state: paused;
		cursor: grab;
	}

	.testimonials-scroll:active {
		cursor: grabbing;
		transform: scale(0.98);
	}

	@keyframes scroll {
		0% { transform: translateY(0); }
		100% { transform: translateY(calc(-33.33%)); }
	}

	.testimonials-scroll > div {
		width: 100%;
		flex-shrink: 0;
	}

	@media (max-width: 768px) {
		.testimonials-scroll {
			gap: 1rem;
			animation-duration: 180s;
		}
	}
</style>





