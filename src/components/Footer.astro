---
import '../styles/animations.css';

const legalLinks = [
  { href: '/cookie-policy/', text: 'Cookie policy' },
  { href: '/privacy-policy/', text: 'Privacy policy' },
  { href: '/acceptable-use-policy/', text: 'Acceptable use policy' },
  { href: '/complaints-handling-procedure/', text: 'Complaints Handling Procedure' }
];

const socialLinks = [
  { href: '#', text: 'Facebook', icon: 'fab fa-facebook' },
  { href: '#', text: 'Instagram', icon: 'fab fa-instagram' },
  { href: '#', text: 'Spotify', icon: 'fab fa-spotify' }
];
---

<footer class="w-full bg-[#003820]/5 footer-section">
  <div class="w-full py-8 lg:py-12">
    <div class="max-w-[90rem] mx-auto px-4 sm:px-6 lg:px-12">
      <div class="grid grid-cols-1 lg:grid-cols-[1.5fr_1fr] gap-8 lg:gap-12">
        <!-- Legal Section -->
        <div class="space-y-6 fade-in">
          <div class="space-y-4">
            <h3 class="text-base font-semibold text-[#003820]">Legal</h3>
            <div class="flex flex-wrap gap-x-2 gap-y-1.5">
              {legalLinks.map((link, index) => (
                <>
                  <a href={link.href} class="text-[#3C4145] text-sm hover:text-[#003820] transition-colors">
                    {link.text}
                  </a>
                  {index < legalLinks.length - 1 && <span class="text-[#3C4145]/50">|</span>}
                </>
              ))}
            </div>
          </div>
          <div class="text-[#3C4145] text-sm space-y-2 leading-relaxed">
            <p>3Pay Global Ltd</p>
            <p>78 York Street, London, W1H 1DP</p>
            <p>Telephone +44 (2)076 9289 77</p>
            <p><EMAIL></p>
            <p>Company registered in England and Wales. Company Number 8615606</p>
          </div>
        </div>

        <!-- Logo and Social Section -->
        <div class="lg:pl-6 lg:border-l border-[#003820]/10 slide-in-right">
          <div class="space-y-6">
            <a href="/" class="block max-w-[160px]">
              <img 
                src="/images/3paylogo.png" 
                alt="3Pay Global Logo" 
                class="w-full h-auto"
              />
            </a>
            <div class="flex gap-4">
              {socialLinks.map((link) => (
                <a 
                  href={link.href} 
                  target="_blank" 
                  rel="noopener"
                  class="text-[#3C4145] hover:text-[#003820] transition-colors text-lg"
                >
                  <i class={link.icon} aria-hidden="true"></i>
                </a>
              ))}
            </div>
          </div>
        </div>
      </div>

      <!-- Disclaimer -->
        <div class="mt-8 pt-6 border-t border-[#003820]/10 fade-in">
        <p class="text-[#3C4145] text-xs max-w-3xl">
          For the avoidance of doubt, we do not conduct or engage in any financial services or other activities regulated by the Financial Conduct Authority.
        </p>
      </div>

      <!-- Footer Bottom -->
        <div class="mt-6 pt-6 border-t border-[#003820]/10 flex flex-col sm:flex-row justify-between items-center gap-3 fade-in">
        <span class="text-[#3C4145] text-xs">© Copyright by 3Pay Global Limited. All Rights Reserved 2024.</span>
        <span class="text-[#3C4145] text-xs">Developed by Termite Tech Labs</span>
      </div>
    </div>
  </div>
</footer>




