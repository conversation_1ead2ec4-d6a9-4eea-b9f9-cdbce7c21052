import type { SubscribersResponse, QuizResponsesResponse, UsersResponse, TypedPocketBase, SolicitorsResponse, UsersUserTypeOptions, UsersLevelOptions } from '@src/data/pocketbase-types'


export const isValidEmail = (email: string) => {
    if (typeof email !== 'string') return false
    if (email.length > 255) return false
    const regex = /^.+@.+$/
    return regex.test(email)
}

export const isValidPassword = (password: string) => {
    if (typeof password !== 'string') return false
    if (password.length < 8 || password.length > 255) return false
    
    // Check for at least one uppercase letter, one lowercase letter, one number, and one special character
    const hasUppercase = /[A-Z]/.test(password)
    const hasLowercase = /[a-z]/.test(password)
    const hasNumber = /\d/.test(password)
    const hasSpecialChar = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]+/.test(password)
    
    return hasUppercase && hasLowercase && hasNumber && hasSpecialChar
}

export function isValidData(
    email: string,
    password: string
) {
    if (!isValidEmail(email)) {
        return false
    }

    if (!isValidPassword(password)) {
        return false
    }

    return true
}

export async function createUser(
    pb: TypedPocketBase,
    email: string,
    password: string,
    user_type: UsersUserTypeOptions,
    fname: string,
    lname: string,
    mobile: string,
    address_line1?: string,
    address_line2?: string,
    city?: string,
    state_province?: string,
    postcode?: string,
    country_code?: string,
    law_firm_name?: string,
    position?: string,
    sra_number?: string,
) {
    return await pb.collection('users').create({
        email,
        password,
        passwordConfirm: password,
        user_type,
        first_name: fname,
        last_name: lname,
        name: `${fname} ${lname}`,
        mobile,
        emailVisibility: true,
        level: "0" as UsersLevelOptions,
        is_permitted_user: false,
        opt_out: false,
        verified: false,
        address_line1: address_line1 || '',
        address_line2: address_line2 || '',
        city: city || '',
        state_province: state_province || '',
        postcode: postcode || '',
        country_code: country_code || '',
        law_firm_name: law_firm_name || '',
        position: position || '',
        sra_number: sra_number || ''
    });
}



export async function createClaim(pb: TypedPocketBase,
    user_id: string,
    claim_industry: string,
    required_funding: string,
    title?: string,
    details?: string
) {
    return await pb.collection('claims').create({
        status: 'pending',
        interest: 'Pre-Action',
        user_id: user_id,
        claim_industry: claim_industry,
        required_funding: required_funding,
        funded_by_data: []
    })
}
export async function createClaimV2(pb: TypedPocketBase,
    data: any
) {
    return await pb.collection('claims').create(data)
}

export async function createSolicitor(
    pb: TypedPocketBase,
    user_id: string,
    law_firm_name: string,
    firm_address: string,
    solicitor_name: string,
    solicitor_position: string,
    contact_number: string,
    sra_number: string
) {
    return await pb.collection('solicitors').create({
        user_id,
        law_firm_name,
        firm_address,
        solicitor_name,
        solicitor_position,
        contact_number,
        sra_number,
        verification_status: 'pending'
    })
}


// const articles = getArticles({level : 'curious'})
export async function createSubscriber(
    pb: TypedPocketBase,
    user_id: string,
    level: string,
) {
    return await pb.collection('subscribers').create({
        user_id: user_id,
        level: level,
        funded_claims_data: [],
        declare_continuing_interest_data: [],
        non_recourse_agreement_data: []
    })
}



export async function loginUser(pb: TypedPocketBase,
    email: string,
    password: string
) {
    return await pb
        .collection('users')
        .authWithPassword(email, password)
}

export async function setCookieAndRedirectToDashboard(pb: TypedPocketBase) {
    return new Response(null, {
        status: 301,
        headers: {
            Location: `/app/get-app`,
            //set secure false on localhost for Safari compatibility
            'Set-Cookie': pb.authStore.exportToCookie({
                secure: import.meta.env.DEV ? false : true,
            }),
        },
    })
}
export async function setCookieAndRedirectToAdminDashboard(pb: TypedPocketBase) {
    return new Response(null, {
        status: 301,
        headers: {
            Location: `/app/admin/dashboard`,
            //set secure false on localhost for Safari compatibility
            'Set-Cookie': pb.authStore.exportToCookie({
                secure: import.meta.env.DEV ? false : true,
            }),
        },
    })
}


export async function isLoggedIn(pb: TypedPocketBase, request: Request) {
    if (!request.headers.get('Cookie')) {
        return false
    }

    pb.authStore.loadFromCookie(
        request.headers.get('Cookie') || '',
        'pb_auth'
    )
    // console.log("Valid Auth Store", pb.authStore.isValid)
    try {
        // get an up-to-date auth store state by veryfing and refreshing the loaded auth model (if any)
        if (
            pb.authStore.isValid &&
            (await pb.collection('users').authRefresh())
        ) {
            return true
        }
    } catch (_) {
        // clear the auth store on failed refresh
        pb.authStore.clear()
    }

    return false
}

export async function getUserUsername(pb: TypedPocketBase, request: Request) {
    pb.authStore.loadFromCookie(
        request.headers.get('Cookie') || '',
        'pb_auth',
    )
    return pb.authStore.model?.username
}

export async function sendResetPasswordLink(pb: TypedPocketBase,email: string) {
    await pb.collection('users').requestPasswordReset(email)
}

export async function getUserObjectFromDb(pb: TypedPocketBase, user_id: string) {
    const user: UsersResponse = await pb.collection('users').getOne(user_id)
    return user
}
export async function getUser(pb: TypedPocketBase, user_id: string) {
    const user: UsersResponse = await pb.collection('users').getFirstListItem(`id="${user_id}"`)
    return user
}

export async function isUserVerified(pb: TypedPocketBase) {
    //we load from db as user object is not updated immediately if user clicks verify email
    const user = await getUserObjectFromDb(pb, getCurrentUserId(pb))
    return user.verified
}

export async function getSubscriberLevel(pb: TypedPocketBase) {
    //we load from db as user object is not updated immediately if user clicks verify email
    const user_id = getCurrentUserId(pb)
    const user_role = await getUserRoleFromCookie(pb)
    if (user_id && user_role.toString() === 'subscriber') {
        try {
            const subscriber: SubscribersResponse = await pb.collection('subscribers').getFirstListItem(`user_id="${user_id}"`)
            return subscriber.level.toString()
        } catch (error) {
            console.error('Error fetching user:', error);
            return null;
        }
    }
}

export async function getSubscriberId(pb: TypedPocketBase){
    const user_id = await getCurrentUserId(pb)
    const user_role = await getUserRoleFromCookie(pb)
    if (user_id && user_role === 'subscriber') {
        try {
            const subscriber: SubscribersResponse = await pb.collection('subscribers').getFirstListItem(`user_id="${user_id}"`)
            return subscriber.id
        } catch (error) {
            console.error('Error fetching user:', error);
            return null;
        }
    }
}

export async function getSolicitorId(pb: TypedPocketBase) {
    const user_id = getCurrentUserId(pb)
    const user_role = await getUserRoleFromCookie(pb)
    if (user_id && user_role === 'solicitor') {
      try {
        const solicitor: SolicitorsResponse = await pb.collection('solicitors').getFirstListItem(`user_id="${user_id}"`)
        return solicitor.id
      } catch (error) {
        console.error('Error fetching user:', error);
        return null;
      }
    }
  }
  

export async function getSubscriber(pb: TypedPocketBase, subscriber_id?: string) {
    const user_id = getCurrentUserId(pb)
    const user_role = await getUserRoleFromCookie(pb)
    if (user_id && user_role.toString() === 'subscriber') {
        try {
            const subscriber: SubscribersResponse = await pb.collection('subscribers').getFirstListItem(`user_id="${user_id}"`, {expand: "user_id"})
            return subscriber
        } catch (error) {
            console.error('Error fetching user:', error);
            return null;
        }
    }
    if (subscriber_id && user_role.toString() === 'admin'){
        try {
            const subscriber: SubscribersResponse = await pb.collection('subscribers').getFirstListItem(`id="${subscriber_id}"`, {expand: "user_id"})
            return subscriber
        } catch (error) {
            console.error('Error fetching user:', error);
            return null;
        }
    }
}
export async function getSolicitor(pb: TypedPocketBase, solicitor_id?: string) {
    const user_id = getCurrentUserId(pb)
    const user_role = await getUserRoleFromCookie(pb)
    if (user_id && user_role.toString() === 'solicitor') {
        try {
            const solicitor: SolicitorsResponse = await pb.collection('solicitors').getFirstListItem(`user_id="${user_id}"`, {expand: "user_id"})
            return solicitor
        } catch (error) {
            console.error('Error fetching user:', error);
            return null;
        }
    }
    if (solicitor_id && user_role.toString() === 'admin'){
        try {
            const solicitor: SolicitorsResponse = await pb.collection('solicitors').getFirstListItem(`id="${solicitor_id}"`, {expand: "user_id"})
            return solicitor
        } catch (error) {
            console.error('Error fetching user:', error);
            return null;
        }
    }
}
export async function getSubscriberQuizId(pb: TypedPocketBase) {
    const subscriber_id = await getSubscriberId(pb)
    const user_role = await getUserRoleFromCookie(pb)
    if (subscriber_id && user_role.toString() === 'subscriber') {
        try {
            const response: QuizResponsesResponse = await pb.collection('quiz_responses').getFirstListItem(`subscriber_id="${subscriber_id}"`)
            return response.id
        } catch (error) {
            console.error('Error fetching user:', error);
            return null;
        }
    }
}

export async function isUserApproved(pb: TypedPocketBase) {
    const user = await getUserObjectFromDb(pb, getCurrentUserId(pb))
    return user.verified
}

export async function getUserRoles(pb: TypedPocketBase, email: string) {
    try {
        const user: UsersResponse = await pb.collection('users').getFirstListItem(`email="${email}"`)
        return user.user_type;
    } catch (error) {
        console.error('Error fetching user:', error);
        return null;
    }
}

export async function getUserRoleFromCookie(pb: TypedPocketBase) {
    return pb.authStore.model?.user_type.toString()
}

//utility function to get current user id
export function getCurrentUserId(pb: TypedPocketBase) {
    // console.log("user.id from pb",pb.authStore.model?.id)
    return pb.authStore.model?.id
}
//utility function to get current mobile number 
export function getCurrentUserMobile(pb: TypedPocketBase) {
    return pb.authStore.model?.mobile
}
//utility function to get any user id
export async function getUserId(pb: TypedPocketBase, email: string) {
    const user: UsersResponse = await pb.collection('users').getFirstListItem(`email = '${email}'`)
    return user.id
}

export async function sendVerificationEmail(pb: TypedPocketBase, email: string) {
    // await pb.collection('users').requestVerification(email)
    await pb.collection('users').requestVerification(email)
    console.log("Verification email sent to ", email)
}

export async function getCurrentUserEmail(pb: TypedPocketBase) {
    return pb.authStore.model?.email || ''
}
export async function getCurrentUserName(pb: TypedPocketBase) {
    return pb.authStore.model?.fname + ' ' + pb.authStore.model?.lname || ''
}

export async function processTurnstile(
    cf_turnstile_response: string
) {
    const url =
        'https://challenges.cloudflare.com/turnstile/v0/siteverify'

    const requestBody = new URLSearchParams({
        secret:
            import.meta.env.TURNSTILE_SITE_SECRET ||
            process.env.TURNSTILE_SITE_SECRET,
        response: cf_turnstile_response
    })
    const response = await fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: requestBody.toString()
    })

    const data = await response.json()

    return data.success
}

export async function createCoFunder(
    pb: TypedPocketBase,
    user_id: string,
    mobile?: string,
) {
    return await pb.collection('co_funders').create({
        user_id,
        mobile: mobile || '',
        level: "0",
        kyc_status: "pending",
        level1_approved: false,
        level1_requested: false,
        level2_approved: false,
        level2_requested: false,
        test_passed: false,
        signed_nda: false,
        associate: false,
        associate_tc_agreed: false
    });
}