---
import Layout from './Layout.astro'
import '@styles/animations.css'

const { title, date, image, previousPost, previousPostTitle, nextPost, nextPostTitle } = Astro.props

const formattedDate = (date: string) =>
  new Date(date).toLocaleDateString('en-us', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  })
---

<Layout title={title}>
  <div id="markdown" class="relative w-full overflow-hidden">
    <section class="relative py-20 bg-[#003820]/5">
      <!-- Background effects remain the same -->
      <div class="absolute inset-0 overflow-hidden pointer-events-none">
        <div class="absolute top-0 left-1/4 w-[45rem] h-[45rem] bg-gradient-to-br from-[#003820]/10 to-[#003820]/5 rounded-full blur-[160px] mix-blend-soft-light animate-float-slow"></div>
        <div class="absolute bottom-0 right-1/4 w-[35rem] h-[35rem] bg-gradient-to-tr from-[#003820]/15 to-[#003820]/5 rounded-full blur-[128px] mix-blend-soft-light animate-float-reverse"></div>
        <div class="absolute inset-0 bg-grid opacity-[0.015] mix-blend-overlay"></div>
      </div>

      <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10 mt-10">
        <div class="max-w-3xl mx-auto">
          <h1 class="text-5xl md:text-7xl font-bold mb-6 text-center animate-gradient bg-[length:200%_auto] bg-clip-text text-transparent bg-gradient-to-r from-[#003820] via-[#005732] to-[#003820]">
            {title}
          </h1>
          <div class="flex justify-center items-center space-x-4 text-[#3C4145]/80 mb-12">
            <span class="text-lg">{formattedDate(date)}</span>
          </div>
          
          {image && (
            <div class="mb-12 premium-card transform-3d hover-scale-105 rounded-xl overflow-hidden">
              <img 
                src={image} 
                alt={title}
                class="w-full h-auto"
                loading="lazy"
                decoding="async"
              />
            </div>
          )}

          <div class="prose prose-lg max-w-none text-[#3C4145]/80 animate-fade-in">
            <slot />
          </div>

          <!-- Navigation section remains the same -->
          <div class="mt-16 border-t border-[#003820]/10 pt-8">
            <nav class="flex justify-between items-center text-[#003820]">
              {previousPost && (
                <a href={`/blog/${previousPost}/`} class="group flex items-center hover:text-[#005732] transition-colors">
                  <svg class="w-5 h-5 mr-2 transform transition-transform group-hover:-translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                  </svg>
                  {previousPostTitle}
                </a>
              )}
              {nextPost && (
                <a href={`/blog/${nextPost}/`} class="group flex items-center hover:text-[#005732] transition-colors ml-auto">
                  {nextPostTitle}
                  <svg class="w-5 h-5 ml-2 transform transition-transform group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                  </svg>
                </a>
              )}
            </nav>
          </div>
        </div>
      </div>
    </section>
  </div>
</Layout>

<style>
  .bg-grid {
    background-image: linear-gradient(rgba(0, 56, 32, 0.05) 1px, transparent 1px),
      linear-gradient(90deg, rgba(0, 56, 32, 0.05) 1px, transparent 1px);
    background-size: 32px 32px;
    background-position: center center;
  }

  /* Card and animation styles remain the same */
  .premium-card {
    transform-style: preserve-3d;
    perspective: 1000px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: transform, box-shadow;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
    backdrop-filter: blur(10px);
  }

  .prose {
    max-width: none;
    font-size: 1.125rem;
    line-height: 1.8;
    color: #3C4145;
    padding: 0 1rem;
  }

  /* Key spacing fixes */
  .prose > * {
    margin-top: 0;
    margin-bottom: 0;
  }

  .prose > * + * {
    margin-top: 1.5em;
  }

  /* Paragraph specific spacing */
  .prose p {
    margin: 0;
    padding: 0;
    line-height: 1.8;
    font-size: 1.125rem;
  }

  .prose p + p {
    margin-top: 2em;  /* Increased spacing between paragraphs */
  }

  /* Heading specific spacing and styling */
  .prose h2,
  .prose .wp-block-heading {
    font-size: 1.75rem;
    font-weight: 700;
    color: #003820;
    margin: 3em 0 1.5em 0;  /* Increased spacing around headings */
    padding: 0;
    line-height: 1.3;
    /* Remove any existing decorative elements */
    border: none;
    background: none;
    position: static;
    display: block;
  }

  /* First heading should have less top margin */
  .prose > h2:first-child,
  .prose > .wp-block-heading:first-child {
    margin-top: 1.5em;
  }

  /* Ensure content after headings has proper spacing */
  .prose h2 + p,
  .prose .wp-block-heading + p {
    margin-top: 1.5em;
  }

  /* Clear any floats that might affect spacing */
  .prose h2::after,
  .prose .wp-block-heading::after {
    content: none;
  }

  /* Adjusted spacing for mobile */
  @media (max-width: 767px) {
    .prose {
      font-size: 1rem;
    }

    .prose p + p {
      margin-top: 1.5em;
    }

    .prose h2,
    .prose .wp-block-heading {
      font-size: 1.5rem;
      margin: 2.5em 0 1.25em 0;
    }
  }

  /* Enhanced link styling */
  .prose a {
    color: #005732;
    text-decoration: none;
    border-bottom: 2px solid rgba(0, 87, 50, 0.2);
    transition: all 0.2s ease;
    padding-bottom: 2px;
  }

  .prose a:hover {
    border-bottom-color: #005732;
    background-color: rgba(0, 87, 50, 0.05);
  }

  /* List spacing */
  .prose ul,
  .prose ol {
    margin: 2em 0;
    padding-left: 1.5em;
  }

  .prose li {
    margin: 0.75em 0;
    padding-left: 0.5em;
  }

  /* Remove default margins that might interfere */
  .prose > :first-child {
    margin-top: 0;
  }

  .prose > :last-child {
    margin-bottom: 0;
  }
</style>