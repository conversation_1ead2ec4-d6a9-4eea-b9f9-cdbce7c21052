---
import '@styles/base.css';
import '@styles/animations.css';
import '@styles/global.css';
import Navbar from '@components/Navbar.astro';
import Footer from '@components/Footer.astro';

interface Props {
  title: string;
}

const { title } = Astro.props;
---

<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="theme-color" content="#003820" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="generator" content={Astro.generator} />
    <title>{title}</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://unpkg.com/aos@2.3.1/dist/aos.css" />
  </head>
  <body class="min-h-screen bg-white antialiased">
    <Navbar />
    <slot />

     <!-- Footer -->
     <section class="relative bg-white">
      <div class="relative z-10" data-aos="fade-up">
        <Footer />
      </div>
    </section>
    
    <script is:inline src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script is:inline>
      window.addEventListener('load', () => {
        AOS.init({
          duration: 1000,
          easing: 'ease-out-cubic',
          once: true,
          mirror: false,
          offset: 50,
          delay: 100,
          anchorPlacement: 'top-bottom',
          startEvent: 'DOMContentLoaded'
        });
      });
    </script>
  </body>
</html>

