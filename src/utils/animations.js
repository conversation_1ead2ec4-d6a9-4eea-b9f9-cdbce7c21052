export function setupIntersectionObserver(element, animationClass = 'fade-in') {
	const observer = new IntersectionObserver(
		(entries) => {
			entries.forEach((entry) => {
				if (entry.isIntersecting) {
					entry.target.classList.add('visible');
					observer.unobserve(entry.target);
				}
			});
		},
		{
			threshold: 0.1,
			rootMargin: '50px',
		}
	);

	const animatedElements = element.querySelectorAll(`.${animationClass}`);
	animatedElements.forEach((el) => {
		observer.observe(el);
	});
}
