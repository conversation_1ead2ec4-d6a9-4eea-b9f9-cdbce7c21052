import { defineMiddleware } from 'astro/middleware'
import { isLoggedIn, isUserVerified } from '@lib/auth'
import PocketBase from 'pocketbase'
import type { TypedPocketBase } from '@data/pocketbase-types'

export const onRequest = defineMiddleware(
    async (context, next) => {

        context.locals.pb = new PocketBase(
            import.meta.env.POCKETBASE_URL || process.env.POCKETBASE_URL
          ) as TypedPocketBase

        // globally disable auto cancellation
        context.locals.pb.autoCancellation(false)
        
            if (await isLoggedIn(context.locals.pb, context.request)) {
                const verified = await isUserVerified(context.locals.pb)
                if (!verified) {
                    if (context.url.pathname.startsWith('/app')) {
                        return context.redirect('/verify')
                    }
                } else {
                    if (context.url.pathname === '/verify') {
                        return context.redirect(`/app/get-app`)
                    }
                }
                // if (context.url.pathname.startsWith('/app/admin/login')) {
                //     return context.redirect('/app/admin/dashboard')
                // }
            }
            return next()
        }
    
)