import { defineConfig } from 'astro/config';
import alpinejs from '@astrojs/alpinejs';
import tailwind from '@astrojs/tailwind';
import node from "@astrojs/node";

export default defineConfig({
	integrations: [
		tailwind({
			config: { path: './tailwind.config.cjs' }
		}),
		alpinejs()
	],
	output: "server",
	adapter: node({
	  mode: "standalone"
	}),
	vite: {
		ssr: {
			noExternal: ['@astrojs/tailwind']
		},
		css: {
			preprocessorOptions: {
				scss: {
					additionalData: `@import "@/styles/base.css";`
				}
			}
		}
	}
});

