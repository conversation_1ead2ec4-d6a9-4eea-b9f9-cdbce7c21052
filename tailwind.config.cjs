/** @type {import('tailwindcss').Config} */
module.exports = {
	content: ['./src/**/*.{astro,html,js,jsx,md,mdx,svelte,ts,tsx,vue}'],
	theme: {
		extend: {
			colors: {
				primary: {
					DEFAULT: '#003820',
					light: '#004d2e',
					dark: '#002716'
				},
				accent: '#00c853'
			},
			maxWidth: {
				'container': '87rem',
				'content': '57rem'
			},
			fontFamily: {
				sans: ['Inter', 'system-ui', 'sans-serif']
			},
			animation: {
				'float': 'float 6s ease-in-out infinite',
				'pulse-slow': 'pulse-slow 4s ease-in-out infinite',
				'spin-slow': 'spin-slow 60s linear infinite',
				'bounce-gentle': 'bounce-gentle 2s ease-in-out infinite',
				'gradient': 'gradient 8s ease infinite',
				'draw': 'draw 1.5s ease-out forwards'
			},
			keyframes: {
				float: {
					'0%, 100%': { transform: 'translateY(0)' },
					'50%': { transform: 'translateY(-10px)' }
				},
				'pulse-slow': {
					'0%, 100%': { opacity: '0.1' },
					'50%': { opacity: '0.2' }
				},
				'spin-slow': {
					'from': { transform: 'rotate(0deg)' },
					'to': { transform: 'rotate(360deg)' }
				},
				'bounce-gentle': {
					'0%, 100%': { transform: 'translateY(-5%)' },
					'50%': { transform: 'translateY(5%)' }
				},
				gradient: {
					'0%': { backgroundPosition: '0% 50%' },
					'50%': { backgroundPosition: '100% 50%' },
					'100%': { backgroundPosition: '0% 50%' }
				},
				draw: {
					'0%': { strokeDasharray: '0 100' },
					'100%': { strokeDasharray: '100 0' }
				}
			},
			boxShadow: {
				'3xl': '0 35px 60px -15px rgba(0, 56, 32, 0.3)'
			}
		}
	},
	plugins: []
}